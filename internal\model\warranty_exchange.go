package model

import "time"

type WarrantyExchange struct {
	ID            int       `gorm:"column:id;primaryKey;autoIncrement"`
	Barcode       string    `gorm:"column:barcode;size:32;not null" json:"barcode"`
	BarcodeNew    string    `gorm:"column:barcode_new;size:32;not null" json:"barcode_new"`
	Reason        string    `gorm:"column:reason;size:512;not null" json:"reason"`
	WarrantyID    int       `gorm:"column:warranty_id;not null" json:"warranty_id"`
	WarrantyNewID int       `gorm:"column:warranty_new_id;not null" json:"warranty_new_id"`
	UID           int       `gorm:"column:uid;not null" json:"uid"`
	Endpoint      int       `gorm:"column:endpoint;not null" json:"endpoint"`
	ExchangeAt    time.Time `gorm:"column:exchange_at" json:"exchange_at"`
	CreateAt      time.Time `gorm:"column:create_at;default:CURRENT_TIMESTAMP;not null" json:"create_at"`
	IsOverdue     bool      `gorm:"column:is_overdue;not null;default:0" json:"is_overdue"`
	Source        int       `gorm:"column:source;default:0" json:"source"`
}

func (WarrantyExchange) TableName() string {
	return "warranty_exchange"
}
