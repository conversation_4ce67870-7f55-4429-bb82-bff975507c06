package operation

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/utils"
	"marketing/internal/service/operation"
)

type OpUser struct {
	svc operation.OpUserSvcInterface
}

func NewOpUser(svc operation.OpUserSvcInterface) *OpUser {
	return &OpUser{
		svc: svc,
	}
}

func (o *OpUser) GetOpUserList(c *gin.Context) {
	keyWord := e.ReqParamStr(c, "keyword")
	blocked := e.ReqParamInt(c, "blocked", 0)
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")
	list, total := o.svc.GetOpUserList(c, keyWord, blocked, pageNum, pageSize)

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (o *OpUser) Statistics(c *gin.Context) {
	total, blockNum := o.svc.Statistics(c)
	handler.Success(c, gin.H{
		"total":     total,
		"block_num": blockNum,
	})
}

func (o *OpUser) BlockOpUser(c *gin.Context) {
	userIds := make([]int, 0)
	utils.JsonStrToObjectList(e.ReqParamStr(c, "user_ids"), &userIds)

	err := o.svc.BlockOpUser(c, userIds)
	if err != nil {
		handler.Error(c, errors.NewErr("拉黑用户失败:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}
