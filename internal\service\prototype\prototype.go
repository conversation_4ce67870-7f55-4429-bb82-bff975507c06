package prototype

import (
	"errors"
	"fmt"
	api "marketing/internal/api"
	api2 "marketing/internal/api/prototype"
	"marketing/internal/consts"
	endpoint "marketing/internal/dao/endpoint"
	dao "marketing/internal/dao/prototype"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/types"
	"mime/multipart"
	"slices"
	"strconv"
	"time"

	"github.com/spf13/cast"
	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
)

type PrototypeService interface {
	GetPrototype(c *gin.Context, req *api2.PrototypeReq) (*api.PagedResponse[gin.H], error)
	PrototypeOut(c *gin.Context, barcode string) error
	PrototypeStat(c *gin.Context, req *api2.PrototypeReq) (*api.PagedResponse[gin.H], error)
	ImportRenewData(c *gin.Context, file *multipart.FileHeader) (string, error)
	GetPrototypeModelList(c *gin.Context) ([]string, error)
	GetPrototypeDemoDownList(c *gin.Context, req *api2.PrototypeConfigListSearch) ([]*model.PrototypeConfig, int64, error)
	AddPrototypeDemoDown(c *gin.Context, req *api2.PrototypeConfigAddReq) error
}

type prototypeService struct {
	dao             dao.PrototypeInterface
	cache           dao.PrototypeCache
	endpointDao     endpoint.EndpointDao
	prototypeConfig dao.PrototypeConfig
}

func NewPrototypeService(dao dao.PrototypeInterface,
	endpointDao endpoint.EndpointDao,
	prototypeConfig dao.PrototypeConfig,
	prototypeCache dao.PrototypeCache) PrototypeService {
	return &prototypeService{
		dao:             dao,
		cache:           prototypeCache,
		endpointDao:     endpointDao,
		prototypeConfig: prototypeConfig,
	}
}

func (s *prototypeService) GetPrototype(c *gin.Context, req *api2.PrototypeReq) (*api.PagedResponse[gin.H], error) {
	var param dao.ListParams
	param.ModelID = req.ModelID
	param.CreatedAtStart = req.CreatedAtStart
	param.CreatedAtEnd = req.CreatedAtEnd
	param.Page = req.Page
	param.PageSize = req.PageSize
	param.TopAgency = req.TopAgency
	param.SecondAgency = req.SecondAgency
	param.Endpoint = req.Endpoint
	param.Status = req.Status
	param.Type = req.Type
	param.Barcode = req.Barcode
	list, count, err := s.dao.GetListWithAgency(c, &param)
	if err != nil {
		return nil, err
	}
	var agencyIDs, endpointIDs []int
	agencyIDSet := make(map[int]struct{})
	endpointIDSet := make(map[int]struct{})

	for _, item := range list {
		if item.TopAgency != 0 {
			if _, exists := agencyIDSet[item.TopAgency]; !exists {
				agencyIDs = append(agencyIDs, item.TopAgency)
				agencyIDSet[item.TopAgency] = struct{}{}
			}
		}
		if item.SecondAgency != 0 {
			if _, exists := agencyIDSet[item.SecondAgency]; !exists {
				agencyIDs = append(agencyIDs, item.SecondAgency)
				agencyIDSet[item.SecondAgency] = struct{}{}
			}
		}
		if item.Endpoint != 0 {
			if _, exists := endpointIDSet[item.Endpoint]; !exists {
				endpointIDs = append(endpointIDs, item.Endpoint)
				endpointIDSet[item.Endpoint] = struct{}{}
			}
		}
	}

	agencies, err := s.endpointDao.GetAgencies(c, agencyIDs)
	if err != nil {
		return nil, err
	}
	endpoints, err := s.endpointDao.GetEndpointMap(c, endpointIDs)
	if err != nil {
		return nil, err
	}

	var data []gin.H
	prototypeTypeName := consts.GetPrototypeTypeName()
	for _, v := range list {
		var topAgency, secondAgency, endpointName string
		if v.TopAgency != 0 {
			topAgency = agencies[uint(v.TopAgency)]
		}
		if v.SecondAgency != 0 {
			secondAgency = agencies[uint(v.SecondAgency)]
		}
		if v.Endpoint != 0 {
			endpointName = endpoints[uint(v.Endpoint)]
		}
		data = append(data, gin.H{
			"id":            v.ID,
			"barcode":       v.Barcode,
			"model":         v.Model,
			"type":          prototypeTypeName[v.Type],
			"status":        v.Status,
			"top_agency":    topAgency,
			"second_agency": secondAgency,
			"endpoint":      endpointName,
			"created_at":    v.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":    v.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	return api.NewPagedResponse(data, count, req.Page, req.PageSize), nil
}

// PrototypeOut 样机离库
func (s *prototypeService) PrototypeOut(c *gin.Context, barcode string) error {
	pt, err := s.dao.GetByBarcode(c, barcode, 1)
	if errors.Is(err, gorm.ErrRecordNotFound) || pt == nil {
		return appError.NewErr("样机已离库或者不存在")
	}
	if err != nil {
		return err
	}
	var ptMap = make(map[string]any)
	ptMap["status"] = 0
	ptMap["removed_at"] = time.Now()

	err = s.dao.Update(c, pt.ID, ptMap)
	if err != nil {
		return err
	}

	//修改缓存
	if pt.Number != "" {
		err = s.cache.Del(c, pt.Number)
		if err != nil {
			return err
		}
	}

	return nil
}

// PrototypeStat 样机统计(按代理 机型显示总数量，并且返回总代初始数量和换新数量)
func (s *prototypeService) PrototypeStat(c *gin.Context, req *api2.PrototypeReq) (*api.PagedResponse[gin.H], error) {
	var param dao.ListParams
	param.ModelID = req.ModelID
	param.Status = req.Status
	param.CreatedAtStart = req.CreatedAtStart
	param.CreatedAtEnd = req.CreatedAtEnd
	param.TopAgency = req.TopAgency
	param.SecondAgency = req.SecondAgency
	param.Endpoint = req.Endpoint
	param.Type = req.Type
	param.Page = req.Page
	param.PageSize = req.PageSize

	list, count, err := s.dao.PrototypeStatList(c, &param)
	if err != nil {
		return nil, err
	}
	var agencyIDs []int
	for _, item := range list {
		if item.TopAgency != 0 {
			agencyIDs = append(agencyIDs, cast.ToInt(item.TopAgency))
		}
	}

	agencies, err := s.endpointDao.GetAgencies(c, agencyIDs)
	if err != nil {
		return nil, err
	}

	var data []gin.H
	for _, v := range list {
		var topAgency string
		if v.TopAgency != 0 {
			topAgency = agencies[v.TopAgency]
		}
		data = append(data, gin.H{
			"model_id":               v.ModelID,
			"model":                  v.Model,
			"top_agency":             topAgency,
			"prototype_count":        v.PrototypeCount,
			"endpoint_count":         v.EndpointCount,
			"initial_endpoint_count": v.InitialEndpointCount,
			"renew_count":            v.RenewCount,
		})
	}
	return api.NewPagedResponse(data, count, req.Page, req.PageSize), nil
}

// ImportRenewData excel导入样机换新数量
func (s *prototypeService) ImportRenewData(c *gin.Context, file *multipart.FileHeader) (string, error) {
	msg := ""

	// 打开上传的文件
	src, err := file.Open()
	if err != nil {
		return msg, appError.NewErr("打开文件失败: " + err.Error())
	}
	defer func(src multipart.File) {
		err := src.Close()
		if err != nil {
			log.Error("关闭文件失败: " + err.Error())
		}
	}(src)

	// 读取excel文件
	f, err := excelize.OpenReader(src)
	if err != nil {
		return msg, appError.NewErr("解析Excel文件失败: " + err.Error())
	}
	defer func(f *excelize.File) {
		err := f.Close()
		if err != nil {
			log.Error("关闭Excel文件失败: " + err.Error())
		}
	}(f)

	// 获取第一个工作表
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return msg, appError.NewErr("读取工作表失败: " + err.Error())
	}

	// 跳过表头
	if len(rows) <= 1 {
		return "Excel文件不包含有效数据", nil
	}

	// 先收集所有代理名称，一次性查询总代理
	var agencyNames []string
	for i := 1; i < len(rows); i++ {
		if len(rows[i]) >= 1 && rows[i][0] != "" && slices.Contains(agencyNames, rows[i][0]) == false {
			agencyNames = append(agencyNames, rows[i][0])
		}
	}

	// 如果没有有效代理名称，直接返回
	if len(agencyNames) == 0 {
		return "Excel文件不包含有效的代理名称", nil
	}

	// 一次性查询所有总代理
	var agencies []struct {
		ID   uint
		Name string
	}
	err = s.dao.DB().WithContext(c).Model(&model.Agency{}).
		Where("name IN (?) AND level = 1", agencyNames).
		Select("id, name").
		Find(&agencies).Error
	if err != nil {
		return "", appError.NewErr("查询代理失败: " + err.Error())
	}

	// 创建代理名称到ID的映射
	agencyMap := make(map[string]uint)
	for _, agency := range agencies {
		agencyMap[agency.Name] = agency.ID
	}

	// 预先查询已存在的记录，减少单条查询
	var existingRecords []struct {
		TopAgency uint
		Model     string
		ID        uint
	}

	// 收集所有模型名称
	var modelNames []string
	for i := 1; i < len(rows); i++ {
		if len(rows[i]) >= 2 && rows[i][1] != "" {
			modelNames = append(modelNames, rows[i][1])
		}
	}

	if len(modelNames) > 0 {
		err = s.dao.DB().WithContext(c).Model(&model.PrototypeRenew{}).
			Where("model IN (?)", modelNames).
			Select("id, top_agency, model").
			Find(&existingRecords).Error
		if err != nil {
			log.Error("查询现有记录失败: " + err.Error())
			// 继续执行，不阻断流程
		}
	}

	// 创建模型和代理ID到记录ID的映射
	existingRecordMap := make(map[string]uint)
	for _, record := range existingRecords {
		key := strconv.FormatUint(uint64(record.TopAgency), 10) + "_" + record.Model
		existingRecordMap[key] = record.ID
	}

	// 准备批量创建和更新的数据
	var createRecords []model.PrototypeRenew
	var updateRecords []model.PrototypeRenew

	// 处理每一行数据
	for rk, row := range rows {
		if rk == 0 {
			continue // 跳过表头
		}

		if len(row) < 3 {
			msg += "第" + strconv.Itoa(rk+1) + "行数据不完整，跳过处理\n"
			continue
		}

		agencyName := row[0]
		modelName := row[1]

		// 检查代理是否存在
		topAgencyID, exists := agencyMap[agencyName]
		if !exists {
			msg += "第" + strconv.Itoa(rk+1) + "行【" + agencyName + "】：找不到对应的代理，请检查代理名称是否正确\n"
			continue
		}

		// 转换数量
		amount, err := strconv.ParseInt(row[2], 10, 64)
		if err != nil {
			msg += "第" + strconv.Itoa(rk+1) + "行【" + agencyName + "】数量格式错误：" + err.Error() + "\n"
			continue
		}

		// 检查记录是否已存在
		recordKey := strconv.FormatUint(uint64(topAgencyID), 10) + "_" + modelName
		if recordID, exists := existingRecordMap[recordKey]; exists {
			// 更新现有记录
			updateRecords = append(updateRecords, model.PrototypeRenew{
				ID:        int(recordID),
				TopAgency: topAgencyID,
				Model:     modelName,
				Amount:    amount,
				UpdatedAt: time.Now().Format("2006-01-02 15:04:05"),
			})
		} else {
			// 创建新记录
			createRecords = append(createRecords, model.PrototypeRenew{
				TopAgency: topAgencyID,
				Model:     modelName,
				Amount:    amount,
				CreatedAt: time.Now().Format("2006-01-02 15:04:05"),
			})
		}
	}

	// 使用事务处理批量创建和更新
	tx := s.dao.DB().WithContext(c).Begin()

	// 批量创建新记录
	if len(createRecords) > 0 {
		if err := tx.Create(&createRecords).Error; err != nil {
			tx.Rollback()
			return "", appError.NewErr("批量创建记录失败: " + err.Error())
		}
	}

	// 批量更新现有记录
	for _, record := range updateRecords {
		if err := tx.Model(&model.PrototypeRenew{}).Where("id = ?", record.ID).
			Updates(map[string]interface{}{
				"amount":     record.Amount,
				"updated_at": record.UpdatedAt,
			}).Error; err != nil {
			tx.Rollback()
			return "", appError.NewErr("更新记录失败: " + err.Error())
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return "", appError.NewErr("提交事务失败: " + err.Error())
	}

	// 生成结果消息
	successMsg := fmt.Sprintf("成功导入数据：新增 %d 条，更新 %d 条", len(createRecords), len(updateRecords))
	if msg == "" {
		msg = successMsg
	} else {
		msg = successMsg + "\n\n以下是处理中的问题：\n" + msg
	}

	return msg, nil
}

// GetPrototypeModelList 获取机型列表
func (s *prototypeService) GetPrototypeModelList(c *gin.Context) ([]string, error) {
	modelList, err := s.dao.GetPrototypeModelList(c)
	return modelList, err
}

// GetPrototypeDemoDownList 样机下市列表
func (s *prototypeService) GetPrototypeDemoDownList(c *gin.Context, req *api2.PrototypeConfigListSearch) ([]*model.PrototypeConfig, int64, error) {
	return s.prototypeConfig.GetPrototypeDemoDownList(c, req)
}

func (s *prototypeService) AddPrototypeDemoDown(c *gin.Context, req *api2.PrototypeConfigAddReq) error {
	var data model.PrototypeConfig
	data.ModelID = req.ModelID
	data.Discontinued = 1
	data.DiscontinuedDate = types.CustomTime(time.Now())
	return s.prototypeConfig.AddPrototypeConfig(c, &data)
}
