package admin

import (
	"github.com/gin-gonic/gin"

	repo "marketing/internal/dao"
	repo1 "marketing/internal/dao/admin_user"
	repo2 "marketing/internal/dao/endpoint"
	repo3 "marketing/internal/dao/prototype"
	"marketing/internal/handler/warranty"
	"marketing/internal/pkg/db"
	service "marketing/internal/service"
)

type WarrantyRouter struct {
	warrantyHandler warranty.WarrantyHandler
}

func NewWarrantyRouter() *WarrantyRouter {
	var Db = db.GetDB()
	warrantyRepo := repo.NewWarrantyDao(Db)
	userRepo := repo1.NewUserDao(Db)
	endpointRepo := repo2.NewEndpointDao(Db)
	prototypeRepo := repo3.NewPrototypeDao(Db)
	machineTypeRepo := repo.NewMachineTypeDao()
	commonRepo := repo.NewCommonGorm(Db)
	warrantySvc := service.NewWarrantyService(warrantyRepo, userRepo, endpointRepo, prototypeRepo, machineTypeRepo, commonRepo)
	warrantyHandler := warranty.NewWarrantyHandler(warrantySvc)
	return &WarrantyRouter{
		warrantyHandler: warrantyHandler,
	}
}

func (wr *WarrantyRouter) Register(r *gin.RouterGroup) {

	w := r.Group("/warranty")
	{
		w.POST("/changeStatus", wr.warrantyHandler.ChangeStatus)
		w.GET("/import", wr.warrantyHandler.Import)
		w.GET("/group_import", wr.warrantyHandler.ImportGroup)
		w.POST("/import", wr.warrantyHandler.DoImport)
		w.POST("/group_import", wr.warrantyHandler.DoImportGroup)
		w.GET("/cancel_insurance", wr.warrantyHandler.CancelInsurance)
		w.GET("/return", wr.warrantyHandler.Return)
		w.POST("/return", wr.warrantyHandler.DoReturn)
		w.GET("/exchange", wr.warrantyHandler.Exchange)
		w.GET("/endpoints", wr.warrantyHandler.Endpoints) // 搜索终端
		w.GET("/export", wr.warrantyHandler.WarrantyExport)

		// 资源型路由
		w.GET("/", wr.warrantyHandler.Index)
		w.POST("/create", wr.warrantyHandler.Create)                   // 创建保单
		w.GET("/list", wr.warrantyHandler.List)                        // 查询保单
		w.PUT("/:id/:assessment", wr.warrantyHandler.UpdateAssessment) // 更新保单的评估状态
		w.PUT("/:id/edit", wr.warrantyHandler.Update)                  // 编辑保单
		w.DELETE("/:id", wr.warrantyHandler.Delete)
	}

	//// 退货
	//warrantyReturn := r.Group("/warranty_return")
	//{
	//	warrantyReturn.GET("/import", wr.warrantyReturnHandler.Import)
	//	warrantyReturn.POST("/import", wr.warrantyReturnHandler.DoImport)
	//	// Resource routes
	//	warrantyReturn.GET("/", wr.warrantyReturnHandler.Index)
	//	warrantyReturn.POST("/", wr.warrantyReturnHandler.Store)
	//	warrantyReturn.GET("/:id", wr.warrantyReturnHandler.Show)
	//	warrantyReturn.PUT("/:id", wr.warrantyReturnHandler.Update)
	//	warrantyReturn.DELETE("/:id", wr.warrantyReturnHandler.Delete)
	//}
	//
	//// 换货
	//warrantyExchange := r.Group("/warranty_exchange")
	//{
	//	// Resource routes
	//	warrantyExchange.GET("/", wr.warrantyExchangeHandler.Index)
	//	warrantyExchange.POST("/", wr.warrantyExchangeHandler.Store)
	//	warrantyExchange.GET("/:id", wr.warrantyExchangeHandler.Show)
	//	warrantyExchange.PUT("/:id", wr.warrantyExchangeHandler.Update)
	//	warrantyExchange.DELETE("/:id", wr.warrantyExchangeHandler.Delete)
	//}
}
