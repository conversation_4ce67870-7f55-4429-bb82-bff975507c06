package service

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"marketing/internal/api/endpoint"
	api "marketing/internal/api/warranty"
	"marketing/internal/consts"
	"marketing/internal/pkg/mes"
	"marketing/internal/pkg/types"
	"strings"

	"regexp"
	"time"

	machineTypeRepo "marketing/internal/dao"
	repo "marketing/internal/dao"
	userRepo "marketing/internal/dao/admin_user"
	endpointRepo "marketing/internal/dao/endpoint"
	prototypeRepo "marketing/internal/dao/prototype"
	warrantyModel "marketing/internal/model"

	"marketing/internal/pkg/errors"
)

type InterfaceWarranty interface {
	Create(c *gin.Context, params *api.CreateWarrantyReq) (map[string]interface{}, error) // 新增保卡
	GetEndpoints(c *gin.Context, name string) []*api.GetEndpointResp
	GetInfo(c *gin.Context, info *api.WarrantyInfoReq) (gin.H, error)  // 获取保卡
	Update(c *gin.Context, id int, params *api.EditReq) (int64, error) // 更新保卡是否纳入考核
	UpdateAssessment(c *gin.Context, id int, action uint) error
}
type warranty struct {
	repo           repo.InterfaceWarranty
	userDao        userRepo.UserDao
	endpointDao    endpointRepo.EndpointDao
	prototypeDao   prototypeRepo.PrototypeInterface
	machineTypeDao machineTypeRepo.MachineTypeDao
	commonDao      repo.CommonDao
}

// NewWarrantyService 创建一个新的保修数据访问对象
func NewWarrantyService(repo repo.InterfaceWarranty,
	userRepo userRepo.UserDao,
	endpointDao endpointRepo.EndpointDao,
	prototypeDao prototypeRepo.PrototypeInterface,
	machineTypeDao machineTypeRepo.MachineTypeDao,
	commonDao repo.CommonDao,
) InterfaceWarranty {
	return &warranty{
		repo:           repo,
		userDao:        userRepo,
		endpointDao:    endpointDao,
		prototypeDao:   prototypeDao,
		machineTypeDao: machineTypeDao,
		commonDao:      commonDao,
	}
}

func (w *warranty) Create(c *gin.Context, parms *api.CreateWarrantyReq) (map[string]interface{}, error) {
	var pending *api.Warranty
	buyDate, err := time.Parse("2006-01-02", parms.Date)
	if err != nil {
		return nil, errors.NewErr("非法日期格式")
	}
	studentBirthday, err := time.Parse("2006-01-02", parms.StudentBirthday)
	if err != nil {
		return nil, errors.NewErr("非法日期格式")
	}
	pending = &api.Warranty{
		Barcode:         parms.Barcode,
		Endpoint:        int(parms.EndpointID),
		BuyDate:         buyDate,
		CustomerPhone:   parms.Phone,
		CustomerName:    parms.Name,
		CustomerSex:     parms.Sex,
		PurchaseWay:     parms.PurchaseWay,
		StudentName:     parms.StudentName,
		StudentSchool:   parms.StudentSchool,
		StudentGrade:    parms.StudentGrade,
		StudentBirthday: studentBirthday,
		StudentSex:      parms.StudentSex,
	}
	if err := validParamsIn(pending); err != nil {
		return nil, err
	}
	salesmanID, err := w.hasSalesman(c, pending)
	if err != nil {
		return nil, err
	}
	endpointInfo, err := w.hasPermissionToAddWarranty(c, salesmanID)
	if err != nil {
		return nil, err
	}
	err = w.allowPrototypeEntryCheck(c, pending)
	if err != nil {
		return nil, err
	}

	// 处理保卡录入
	resp, err := w.processWarrantyEntry(c, pending, endpointInfo, salesmanID)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (w *warranty) GetEndpoints(c *gin.Context, name string) []*api.GetEndpointResp {
	resp := w.endpointDao.GetEndpointByName(c, name)
	if len(resp) == 0 {
		return nil
	}
	return resp
}

func (w *warranty) GetInfo(c *gin.Context, query *api.WarrantyInfoReq) (gin.H, error) {
	var resp []map[string]interface{}
	list, total, page, pageSize, err := w.repo.GetWarrantiesWithEndpoint(c, query)
	if err != nil {
		return nil, err
	}
	for _, we := range list {
		var buyDate string
		if !we.BuyDate.IsZero() {
			buyDate = we.BuyDate.Format("2006-01-02 15:04:05")
		}
		resp = append(resp, gin.H{
			"barcode":          we.Barcode,
			"ext_barcode":      we.ExtBarcode,
			"model":            we.Model,
			"salesman":         we.Salesman,
			"salesman_phone":   we.Phone,
			"customer_name":    we.CustomerName,
			"customer_phone":   we.CustomerPhone,
			"customer_sex":     we.CustomerSex,
			"customer_address": we.CustomerAddress,
			"endpoint_name":    we.Name,
			"endpoint_address": we.Address,
			"endpoint_phone":   we.Phone,
			"endpoint_manager": we.Manager,
			"assessment":       we.Assessment,
			"buy_date":         buyDate,
		})
	}

	return gin.H{
		"total":         total,
		"page":          page,
		"size":          pageSize,
		"warranty_list": resp,
	}, nil
}

func (w *warranty) Update(c *gin.Context, id int, params *api.EditReq) (int64, error) {
	assessment := uint(0)
	update := &warrantyModel.Warranty{Assessment: &assessment}
	if err := fill(update, params); err != nil {
		return 0, err
	}
	update.ID = id
	affected, err := w.repo.UpdateByID(c, update)
	if err != nil {
		return 0, err
	}
	return affected, nil
}

func (w *warranty) UpdateAssessment(c *gin.Context, id int, action uint) error {
	err := w.repo.UpdateAssessment(c, id, action)
	if err != nil {
		return errors.NewErr("更新失败")
	}
	return nil
}

func (w *warranty) processWarrantyEntry(
	c *gin.Context,
	pending *api.Warranty,
	endpointInfo *endpoint.EndpointInfoWithC,
	salesmanID uint,
) (map[string]interface{}, error) {
	// 查询现有保卡（包括副卡）并进行初步校验
	existingWarranty, err := w.repo.HasWarranty(c, pending.Barcode, pending.Imei, pending.Number)
	if err != nil {
		return nil, fmt.Errorf("查询已有保卡失败: %w", err)
	}

	// 处理已存在主卡的情况
	if existingWarranty != nil && !existingWarranty.CreatedAt.IsZero() {
		return nil, errors.NewErr("电子保卡已存在")
	}

	// 校验副机条码（1、pending 中提供的副机条码  2、existingWarranty中的副机条码）
	if pending.ExtBarcode != "" {
		if err := w.validateSecondaryCard(c, pending, existingWarranty); err != nil {
			return nil, err
		}
	}

	// 根据是否有已有保卡（existingWarranty）进行不同的处理
	var resp map[string]interface{}
	var finalWarranty *warrantyModel.Warranty

	if existingWarranty != nil {
		finalWarranty, resp, err = w.processExistingWarranty(c, pending, endpointInfo, salesmanID, existingWarranty)
		if err != nil {
			return nil, err
		}
	} else {
		finalWarranty, resp, err = w.processNewWarranty(c, pending, endpointInfo, salesmanID)
		if err != nil {
			return nil, err
		}
	}

	// 执行数据库操作 (添加或更新保卡)
	var affected int64
	if existingWarranty != nil {
		// 更新保卡
		affected, err = w.repo.UpdateWarranty(c, finalWarranty)
	} else {
		// 添加保卡
		affected, err = w.repo.Add(c, finalWarranty)
	}
	if err != nil || affected == 0 {
		return nil, errors.NewErr(fmt.Sprintf("保存保卡信息失败: %v", err))
	}

	// 激活终端
	if err := w.endpointDao.ActivateEndpoint(c, endpointInfo.ID); err != nil {
		// 激活失败可能不直接阻塞保卡录入，但需要记录或返回错误
		return nil, fmt.Errorf("激活终端失败: %w", err)
	}

	// 补充响应信息
	resp["endpoint_name"] = endpointInfo.Name
	resp["endpoint_address"] = endpointInfo.Address
	resp["end_phone"] = endpointInfo.Phone
	resp["endpoint_manager"] = endpointInfo.Manager
	// TODO: 通知成功添加 notifyAddSuccess
	// TODO: 进销存同步消息 load_contact_warranty
	// TODO: 从保卡数据中导入联系人
	// TODO: 取消样机
	// TODO: 录保卡更新warranty_id字段
	// TODO: 补录保卡(需要前端传来is_supply字段,新版暂时没有)
	return resp, nil
}

func (w *warranty) processExistingWarranty(
	c *gin.Context,
	pending *api.Warranty,
	endpointInfo *endpoint.EndpointInfoWithC,
	salesmanID uint,
	existingWarranty *api.WarrantyWithMT,
) (*warrantyModel.Warranty, map[string]interface{}, error) {
	// 1. 校验特定品类（Category ID 8，agency 渠道，Model 以 "D" 开头）的学生信息
	if existingWarranty.CategoryId == 8 && endpointInfo.Channel == "agency" && strings.HasPrefix(existingWarranty.Model, "D") {
		if pending.StudentName == "" {
			return nil, nil, errors.NewErr("学生姓名不能为空")
		}
		if pending.StudentSchool == "" {
			return nil, nil, errors.NewErr("学生学校不能为空")
		}
		if pending.StudentGrade == "" {
			return nil, nil, errors.NewErr("学生年级不能为空")
		}
	}

	// 2. 查询品类和价格
	modelInfo := w.machineTypeDao.GetMachineTypeByModelId(c, existingWarranty.ModelID)
	if modelInfo == nil || modelInfo.CategoryId == 0 {
		// TODO: 微信通知失败
		return nil, nil, errors.NewErr("未找到机型信息")
	}
	customerPrice := modelInfo.CustomerPrice

	// 3. 计算 assessment
	assessment := uint(0)
	if existingWarranty.CategoryId == 1 {
		// 检查是否在样机库
		count, err := w.prototypeDao.PrototypeCheck(c, pending.Barcode, "") // 假设PrototypeCheck需要imei
		if err != nil {
			return nil, nil, errors.NewErr(fmt.Sprintf("校验样机库失败: %v", err))
		}

		// 判断激活时间是否合法
		if count == 0 && !existingWarranty.ActivatedAtOld.IsZero() && pending.BuyDate.After(existingWarranty.ActivatedAtOld) {
			isDateRangeValid := existingWarranty.ActivatedAtOld.After(time.Date(2021, 1, 31, 0, 0, 0, 0, time.UTC)) ||
				existingWarranty.ActivatedAtOld.Before(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC))
			if isDateRangeValid {
				msg := fmt.Sprintf("录入失败，购买日期不得晚于机器的激活日期。 \n本机的激活时间为%s，请修改购买日期后重新提交。",
					existingWarranty.ActivatedAtOld.Format("2006-01-02"))
				return nil, nil, errors.NewErr(msg)
			}
		}

		// 确定 assessment 值
		if existingWarranty.ActivatedAtOld.IsZero() {
			assessment = 0 // 没有激活时间按正常流程
		} else {
			nowTime := time.Now()
			// 录入时间和购买时间同年同月并且纳入考核
			if pending.BuyDate.Format("2006-01") == nowTime.Format("2006-01") &&
				existingWarranty.ActivatedAtOld.After(time.Date(2021, 1, 31, 0, 0, 0, 0, time.UTC)) {
				assessment = 1
			} else if pending.BuyDate.AddDate(0, 1, 0).Format("2006-01") == nowTime.Format("2006-01") &&
				nowTime.Day() <= 3 &&
				existingWarranty.ActivatedAtOld.After(time.Date(2021, 1, 31, 0, 0, 0, 0, time.UTC)) {
				assessment = 1
			}
		}
	} else {
		// 默认纳入考核
		assessment = 1
	}

	updateWarranty := &warrantyModel.Warranty{
		Barcode:             pending.Barcode,
		Imei:                existingWarranty.Imei,
		ExtBarcode:          pending.ExtBarcode,
		Endpoint:            endpointInfo.ID,
		BuyDate:             pending.BuyDate,
		Assessment:          &assessment,
		Status:              0, // 默认状态
		SalesmanID:          int(salesmanID),
		Salesman:            pending.Salesman,
		CustomerPrice:       customerPrice,
		CustomerName:        pending.CustomerName,
		CustomerSex:         pending.CustomerSex,
		CustomerPhone:       pending.CustomerPhone,
		CustomerAddr:        pending.CustomerAddr,
		StudentUID:          pending.StudentUID,
		CreatedAt:           pending.CreatedAt,
		StudentName:         pending.StudentName,
		StudentSex:          pending.StudentSex,
		StudentSchoolID:     pending.StudentSchoolID,
		StudentSchool:       pending.StudentSchool,
		StudentSchoolAdcode: pending.StudentSchoolAdcode,
		StudentGrade:        pending.StudentGrade,
		StudentBirthday:     types.CustomTime(pending.StudentBirthday),
		PurchaseWay:         pending.PurchaseWay,
		UpdatedAt:           types.CustomTime{},
		Recommender:         pending.Recommender,
		RecommenderPhone:    pending.RecommenderPhone,
		Type:                pending.Type,
		Lng:                 pending.Lng,
		Lat:                 pending.Lat,
	}

	resp := map[string]interface{}{
		"barcode":          pending.Barcode,
		"ext_barcode":      pending.ExtBarcode,
		"salesman":         pending.Salesman,
		"customer_name":    pending.CustomerName,
		"customer_phone":   pending.CustomerPhone,
		"customer_addr":    pending.CustomerAddr,
		"model":            existingWarranty.Model,
		"endpoint":         endpointInfo.ID,
		"buy_date":         pending.BuyDate.Format("2006-01-02"),
		"student_name":     pending.StudentName,
		"student_school":   pending.StudentSchool,
		"student_grade":    pending.StudentGrade,
		"student_birthday": pending.StudentBirthday.Format("2006-01-02"),
		"created_at":       pending.CreatedAt.Format("2006-01-02 15:04:05"),
		"customer_price":   pending.CustomerPrice,
		"customer_sex":     pending.CustomerSex,
		"student_sex":      pending.StudentSex,
		"salesman_id":      salesmanID,
	}

	return updateWarranty, resp, nil
}

func (w *warranty) processNewWarranty(
	c *gin.Context,
	pending *api.Warranty,
	endpointInfo *endpoint.EndpointInfoWithC,
	salesmanID uint,
) (*warrantyModel.Warranty, map[string]interface{}, error) {
	// 查询MES设备信息
	mesDevice, err := w.commonDao.GetMesDevice(c, pending.Barcode, pending.Imei, pending.Number)
	if err != nil || mesDevice == nil {
		return nil, nil, errors.NewErr("无效的条形码或IMEI或序列号")
	}

	// 原逻辑包含副机条码限制，但数据库中没有med_devices_type表
	//if mesDevice.ExtBarcodeCount < 0 {
	//	return nil, nil, errors.NewErr("此条码为副机条码，不可进行保卡录入，请输入对应的主机条码")
	//}
	//if pending.ExtBarcode != "" && mesDevice.ExtBarcodeCount <= 0 {
	//	return nil, nil, errors.NewErr("该机型无需副机条码")
	//}

	// 查询机型信息
	modelInfo := w.machineTypeDao.GetMachineTypeByModelId(c, mesDevice.ModelID)
	if modelInfo == nil || modelInfo.CategoryId == 0 {
		// TODO: 发送微信通知失败
		return nil, nil, errors.NewErr("未找到机型信息")
	}

	// 校验特定品类的学生信息
	if modelInfo.CategoryId == 8 && endpointInfo.Channel == "agency" && strings.HasPrefix(modelInfo.Name, "D") {
		if pending.StudentName == "" {
			return nil, nil, errors.NewErr("学生姓名不能为空")
		}
		if pending.StudentSchool == "" {
			return nil, nil, errors.NewErr("学生学校不能为空")
		}
		if pending.StudentGrade == "" {
			return nil, nil, errors.NewErr("学生年级不能为空")
		}
	}

	// 确定 assessment 值
	var assessment uint
	if modelInfo.CategoryId == 1 {
		assessment = 0 // 品类 1，默认 assessment 为 0
	} else {
		assessment = 1 // 其他品类，默认 assessment 为 1
	}

	addWarranty := &warrantyModel.Warranty{
		Imei:                mesDevice.Imei1,
		Barcode:             mesDevice.Barcode, // 使用 MES 设备中的 Barcode
		ExtBarcode:          pending.ExtBarcode,
		Salesman:            pending.Salesman,
		CustomerName:        pending.CustomerName,
		CustomerPhone:       pending.CustomerPhone,
		CustomerAddr:        pending.CustomerAddr,
		ModelID:             mesDevice.ModelID,
		Model:               mesDevice.Model,
		Endpoint:            endpointInfo.ID,
		BuyDate:             pending.BuyDate,
		ProductDate:         mesDevice.ProductDate,
		Lng:                 pending.Lng,
		Lat:                 pending.Lat,
		StudentName:         pending.StudentName,
		StudentSchoolAdcode: pending.StudentSchoolAdcode,
		StudentSchoolID:     pending.StudentSchoolID,
		StudentSex:          pending.StudentSex,
		StudentSchool:       pending.StudentSchool,
		StudentGrade:        pending.StudentGrade,
		StudentBirthday:     types.CustomTime(pending.StudentBirthday),
		CustomerPrice:       modelInfo.CustomerPrice,
		SalesmanID:          int(salesmanID),
		Status:              int8(mesDevice.Status),
		Number:              mesDevice.Number,
		CustomerSex:         pending.CustomerSex,
		PurchaseWay:         pending.PurchaseWay,
		CreatedAt:           pending.CreatedAt,
		Recommender:         pending.Recommender,
		RecommenderPhone:    pending.RecommenderPhone,
		Assessment:          &assessment,
		StudentUID:          pending.StudentUID,
		Type:                pending.Type,
	}

	resp := map[string]interface{}{
		"barcode":          mesDevice.Barcode,
		"ext_barcode":      pending.ExtBarcode,
		"number":           mesDevice.Number,
		"salesman":         pending.Salesman,
		"customer_name":    pending.CustomerName,
		"customer_phone":   pending.CustomerPhone,
		"customer_addr":    pending.CustomerAddr,
		"model_id":         mesDevice.ModelID,
		"model":            mesDevice.Model,
		"endpoint":         endpointInfo.ID,
		"buy_date":         pending.BuyDate.Format("2006-01-02"),
		"product_date":     mesDevice.ProductDate.Format("2006-01-02"),
		"student_name":     pending.StudentName,
		"student_school":   pending.StudentSchool,
		"student_grade":    pending.StudentGrade,
		"student_birthday": pending.StudentBirthday.Format("2006-01-02"),
		"created_at":       pending.CreatedAt.Format("2006-01-02 15:04:05"),
		"customer_price":   modelInfo.CustomerPrice,
		"customer_sex":     pending.CustomerSex,
		"student_sex":      pending.StudentSex,
		"salesman_id":      salesmanID,
	}

	return addWarranty, resp, nil

}

func (w *warranty) allowPrototypeEntryCheck(c *gin.Context, pending *api.Warranty) error {
	prototypeInfo, err := w.prototypeDao.GetInfo(c, pending.Barcode) // 判断是否是演示样机
	if err != nil {
		return err
	}
	if prototypeInfo != nil && prototypeInfo.Type == consts.PrototypeTypeDemo && prototypeInfo.Discontinued == consts.PrototypeListingOn {
		return errors.NewErr("演示样机在库不允许录入保卡")
	}
	return nil
}

func (w *warranty) hasPermissionToAddWarranty(c *gin.Context, salesmanID uint) (*endpoint.EndpointInfoWithC, error) {
	endpointInfo, err := w.endpointDao.GetUserEndpointInfo(c, salesmanID)
	if err != nil {
		return nil, errors.NewErr("账号无权限添加电子保卡")
	}
	return endpointInfo, nil
}

func (w *warranty) hasSalesman(c *gin.Context, pending *api.Warranty) (uint, error) {
	// 查询user_endpoint获取UID
	salesmanID, err := w.userDao.GetUserID(c, pending.Endpoint)
	if err != nil || salesmanID == 0 {
		return 0, errors.NewErr("店长信息不存在")
	}

	pending.SalesmanID = int(salesmanID)

	// 查询admin_users获取销售人员
	salesman, err := w.userDao.GetSalesmanByUserID(c, salesmanID)
	if err != nil {
		return 0, errors.NewErr("店长信息不存在")
	}
	pending.Salesman = salesman
	return salesmanID, nil
}

func (w *warranty) validateSecondaryCard(c *gin.Context, pending *api.Warranty, existingWarranty *api.WarrantyWithMT) error {
	// 验证提供的副卡
	extSold, err := w.repo.HasWarranty(c, pending.ExtBarcode, "", "") // 验证副机条码的机器是否存在
	if err != nil {
		return errors.NewErr("查询副机保卡失败")
	}
	if extSold != nil {
		if !extSold.CreatedAt.IsZero() {
			return errors.NewErr("该副机条码电子保卡已存在")
		} else {
			extBarcodeNum := extSold.ExtBarcodeNum
			if extBarcodeNum >= 0 {
				return errors.NewErr("无效的副机条码")
			}
		}
	} else { // 验证副机条码的机器是否有效
		extMachine, err := mes.CheckMachine(mes.CheckMachineParams{
			Barcode: pending.ExtBarcode,
			Imei:    "",
			Number:  "",
		})
		if err != nil || extMachine == nil {
			return errors.NewErr("无效的副机条码")
		}
		if m, ok := extMachine.(map[string]interface{}); ok {
			extBarcodeNum := m["ext_barcode_count"].(int)
			if extBarcodeNum >= 0 {
				return errors.NewErr("无效的副机条码")
			}
		}
	}

	if existingWarranty != nil {
		// 如果已有保卡信息，则使用其 ExtBarcodeNum
		if existingWarranty.ExtBarcodeNum < 0 {
			return errors.NewErr("此条码为副机条码，不可进行保卡录入，请输入对应的主机条码")
		}
		if pending.ExtBarcode != "" && existingWarranty.ExtBarcodeNum <= 0 {
			return errors.NewErr("该机型无需副机条码")
		}
	}

	return nil
}

func validParamsIn(in *api.Warranty) error {

	if in.Barcode == "" {
		return errors.NewErr("序列号不能为空")
	}
	if in.Endpoint == 0 {
		return errors.NewErr("终端地址不能为空")
	}
	if in.CustomerPhone == "" {
		return errors.NewErr("手机不能为空")
	}
	if in.CustomerName == "" {
		return errors.NewErr("姓名不能为空")
	}

	if in.Type == 0 {
		in.Type = 1
	}
	now := time.Now()
	buy := in.BuyDate
	in.CreatedAt = now
	if buy.IsZero() {
		now := time.Now()
		in.BuyDate = now
	} else {
		if buy.After(time.Now()) {
			return errors.NewErr("购机时间不能超过当前时间")
		}
		if buy.AddDate(2, 0, 0).Before(time.Now()) {
			return errors.NewErr("购机时间不能超过两年")
		}
	}

	if in.Barcode != "" && !isBarcode(in.Barcode) {
		return errors.NewErr("条码格式错误")
	}
	if in.ExtBarcode != "" && !isBarcode(in.ExtBarcode) {
		return errors.NewErr("副机条码错误")
	}
	if in.Imei != "" && !isImei(in.Imei) {
		return errors.NewErr("IMEI格式错误")
	}
	if in.Number != "" && !isSerialNumber(in.Number) {
		return errors.NewErr("序列号错误")
	}
	if !isPhone(in.CustomerPhone) {
		return errors.NewErr("电话格式错误")
	}
	if in.StudentGrade != "" && !consts.IsGradeValid(in.StudentGrade) {
		return errors.NewErr("年级错误")
	}
	if !in.StudentBirthday.IsZero() && in.StudentBirthday.After(now) {
		return errors.NewErr("学生生日不能超过当前时间")
	}

	return nil
}

// 判断条码格式，5-14位字母或数字
func isBarcode(barcode string) bool {
	matched, _ := regexp.MatchString(`^[A-Za-z-0-9]{6,14}$`, barcode)
	return matched
}

// 判断IMEI格式，14位数字
func isImei(imei string) bool {
	matched, _ := regexp.MatchString(`^\d{14}$`, imei)
	return matched
}

// 判断序列号格式，7-64位字母或数字
func isSerialNumber(number string) bool {
	matched, _ := regexp.MatchString(`^[A-Za-z-1-9]{8,64}$`, number)
	return matched
}

// 判断是否为性别
func isSex(sex string) bool {
	if sex != consts.Male && sex != consts.Female {
		return false
	}
	return true
}

// 判断手机号格式，1开头的11位数字
func isPhone(phone string) bool {
	matched, _ := regexp.MatchString(`^1[0-9]{10}$`, phone)
	return matched
}

func fill(update *warrantyModel.Warranty, params *api.EditReq) error {
	if params.ExtBarcode != "" {
		update.ExtBarcode = params.ExtBarcode
	}
	if params.CustomerName != "" {
		update.CustomerName = params.CustomerName
	}
	if params.CustomerPhone != "" {
		update.CustomerPhone = params.CustomerPhone
	}
	if params.CustomerSex != "" {
		if !isSex(params.CustomerSex) {
			return errors.NewErr("性别错误")
		}
		update.CustomerSex = params.CustomerSex
	}
	if params.PurchaseWay != "" {
		update.PurchaseWay = params.PurchaseWay
	}
	if params.StudentName != "" {
		update.StudentName = params.StudentName
	}
	if params.StudentSchool != "" {
		update.StudentSchool = params.StudentSchool
	}
	if params.StudentGrade != "" {
		update.StudentGrade = params.StudentGrade
	}
	if params.StudentBirthday != "" {
		date, err := time.Parse("2006-01-02", params.StudentBirthday)
		if err != nil {
			return err
		}
		update.StudentBirthday = types.CustomTime(date)
	}
	if params.StudentSex != "" {
		if !isSex(params.CustomerSex) {
			return errors.NewErr("性别错误")
		}
		update.StudentSex = params.StudentSex
	}

	if params.Assessment == consts.WarrantyAssessment {
		*(update.Assessment) = 1
	} else {
		*(update.Assessment) = 0
	}
	return nil
}
