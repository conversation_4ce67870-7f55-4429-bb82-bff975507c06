package dao

import (
	errors2 "errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	api "marketing/internal/api/warranty"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	zaplog "marketing/internal/pkg/log"
	"marketing/internal/pkg/types"
	"strconv"
	"time"
)

type InterfaceWarranty interface {
	GetByBarcode(c *gin.Context, barcode string) (*model.Warranty, error)
	GetWarrantiesWithEndpoint(c *gin.Context, params *api.WarrantyInfoReq) ([]*api.WarrantyInfoWithEndpoint, int64, int, int, error)
	HasWarranty(c *gin.Context, barcode string, imei string, phone string) (*api.WarrantyWithMT, error)
	UpdateWarranty(c *gin.Context, params *model.Warranty) (int64, error) // 更新一条已有保单保单‘
	UpdateByID(c *gin.Context, update *model.Warranty) (int64, error)
	Add(c *gin.Context, params *model.Warranty) (int64, error)
	UpdateAssessment(c *gin.Context, id int, action uint) error // 更新考核状态
}

type warranty struct {
	db *gorm.DB
}

func NewWarrantyDao(db *gorm.DB) InterfaceWarranty {
	return &warranty{
		db: db,
	}
}

func (w *warranty) UpdateWarranty(c *gin.Context, wt *model.Warranty) (int64, error) {
	now := time.Now()
	wt.UpdatedAt = types.CustomTime(now)

	wt.Status = 1
	res := w.db.WithContext(c).
		Model(&model.Warranty{}).
		Omit("barcode", "model").
		Where("barcode = ? AND status = 0", wt.Barcode).
		Updates(wt)
	if res.Error != nil {
		return 0, res.Error
	}

	return res.RowsAffected, nil
}

func (w *warranty) UpdateByID(c *gin.Context, update *model.Warranty) (int64, error) {
	now := time.Now()
	update.UpdatedAt = types.CustomTime(now)
	update.Status = 1

	whereCond := model.Warranty{ID: update.ID}
	var record model.Warranty
	if err := w.db.Where(whereCond).First(&record).Error; err != nil {
		if errors2.Is(err, gorm.ErrRecordNotFound) {
			return 0, errors.NewErr("record not found")
		}
		return 0, err
	}

	res := w.db.WithContext(c).Model(&record).Updates(update)
	if res.Error != nil {
		return 0, res.Error
	}
	return res.RowsAffected, nil
}

func (w *warranty) GetByBarcode(c *gin.Context, barcode string) (*model.Warranty, error) {
	var warranty *model.Warranty

	err := w.db.WithContext(c).Table("warranty as w").
		Select("w.id, w.barcode, w.number, w.imei, w.ext_barcode, w.created_at, w.status, w.model, w.model_id, w.customer_phone, w.product_date, mt.category_id, mt.ext_barcode_num, w.state, w.activated_at_old").
		Joins("LEFT JOIN machine_type mt ON w.model_id = mt.model_id").
		Where("(w.barcode = ? OR w.ext_barcode = ?) AND (w.status = 1 OR w.status = 0)", barcode, barcode).
		First(&warranty).Error

	if err != nil {
		return nil, err
	}

	return warranty, nil
}

func (w *warranty) HasWarranty(c *gin.Context, barcode string, imei string, number string) (*api.WarrantyWithMT, error) {
	var wt api.WarrantyWithMT
	tx := w.db.WithContext(c).Table("warranty as w").
		Select("w.id, w.barcode, w.number, w.imei, w.ext_barcode, w.created_at, w.status, w.model, w.model_id, w.customer_phone, w.product_date, w.state, w.activated_at_old, mt.category_id, mt.ext_barcode_num").
		Joins("LEFT JOIN machine_type mt ON w.model_id = mt.model_id")

	if barcode != "" {
		tx = tx.Where("(w.barcode = ? OR w.ext_barcode = ?) AND (w.status = 1 OR w.status = 0)", barcode, barcode) // 有主卡或副卡
	} else if imei != "" {
		tx = tx.Where("w.imei = ? AND (w.status = 1 OR w.status = 0)", imei)
	} else {
		tx = tx.Where("w.number = ? AND (w.status = 1 OR w.status = 0)", number)
	}

	err := tx.First(&wt).Error
	if err != nil {
		if errors2.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &wt, nil
}

func (w *warranty) Add(c *gin.Context, wt *model.Warranty) (int64, error) {
	res := w.db.WithContext(c).Create(wt)
	if res.Error != nil || res.RowsAffected == 0 {
		return 0, errors.NewErr("insert nothing")
	}
	// TODO:如果价格为0，发送微信通知
	return res.RowsAffected, nil
}

func (w *warranty) GetWarrantiesWithEndpoint(c *gin.Context, params *api.WarrantyInfoReq) ([]*api.WarrantyInfoWithEndpoint, int64, int, int, error) {
	query := w.db.WithContext(c).Table("warranty as w").
		Select("w.barcode, w.ext_barcode, w.model, w.salesman, w.buy_date, w.customer_name, w.customer_phone, w.customer_addr, w.customer_sex, endpoint.name, endpoint.address, endpoint.phone, endpoint.manager, w.assessment").
		Joins("LEFT JOIN endpoint ON w.endpoint = endpoint.id").
		Where("w.status = ?", 1)

	if barcode := params.Barcode; barcode != "" {
		query = query.Where("barcode LIKE ?", barcode+"%")
	}
	if customerName := params.CustomerName; customerName != "" {
		query = query.Where("customer_name LIKE ?", "%"+customerName+"%")
	}
	if customerPhone := params.CustomerPhone; customerPhone != "" {
		query = query.Where("customer_phone = ?", customerPhone)
	}
	if begin := params.BuyDateStart; begin != "" {
		b, err := time.Parse("2006-01-02", begin)
		if err != nil {
			return nil, 0, 0, 0, errors.NewErr("非法日期格式")
		}
		query = query.Where("buy_date >= ?", b)
	}
	if end := params.BuyDateEnd; end != "" {
		e, err := time.Parse("2006-01-02", end)
		if err != nil {
			return nil, 0, 0, 0, errors.NewErr("非法日期格式")
		}
		query = query.Where("buy_date <= ?", e)
	}
	if m := params.Model; m != "" {
		query = query.Where("model = ?", m)
	}
	if topAgency := params.Pid; topAgency != 0 {
		query = query.Where("endpoint.top_agency = ?", topAgency)
	}
	if secondaryAgency := params.SecondaryAgency; secondaryAgency != 0 {
		query = query.Where("endpoint.second_agency = ?", secondaryAgency)
	}
	if endpointCode := params.EndpointCode; endpointCode != "" {
		query = query.Where("endpoint.code = ?", endpointCode)
	}

	query = query.Order("w.id desc")
	var total int64
	page, _ := strconv.Atoi(c.DefaultPostForm("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultPostForm("page_size", "20"))
	offset := (page - 1) * pageSize

	query.Count(&total)
	var warrantiesWithEndpoint []*api.WarrantyInfoWithEndpoint
	err := query.Offset(offset).Limit(pageSize).Scan(&warrantiesWithEndpoint).Error
	if err != nil {
		return nil, 0, 0, 0, err
	}
	return warrantiesWithEndpoint, total, page, pageSize, nil
}

func (w *warranty) UpdateAssessment(c *gin.Context, id int, action uint) error {
	zaplog.Info("action_is:", zap.Uint("action", action))
	var wt model.Warranty
	if err := w.db.WithContext(c).First(&wt, id).Error; err != nil {
		return errors.NewErr("warranty not found")
	}

	warrantyID := id
	endpointID := wt.Endpoint

	err := w.db.WithContext(c).
		Transaction(func(tx *gorm.DB) error {
			zaplog.Info("action_is:", zap.Uint("action", action))
			var wy model.Warranty
			// 锁定 warranty 行
			if err := tx.Clauses(clause.Locking{Strength: "Update"}).First(&wy, "id = ?", warrantyID).Error; err != nil {
				return errors.NewErr("failed to lock warranty")
			}

			newWarrantyUpdateAt := time.Now()
			result := tx.Model(&wy).
				Select("Assessment", "UpdatedAt").
				Updates(
					model.Warranty{
						Assessment: &action,
						UpdatedAt:  types.CustomTime(newWarrantyUpdateAt),
					},
				)
			if result.Error != nil {
				return errors.NewErr("failed to update warranty")
			}
			if result.RowsAffected == 0 {
				return errors.NewErr(fmt.Sprintf("no warranty found with ID: %d", warrantyID))
			}

			// 锁定 Endpoint 行
			var ep model.Endpoint
			if err := tx.Clauses(clause.Locking{Strength: "Update"}).First(&ep, "id = ?", endpointID).Error; err != nil {
				return errors.NewErr("failed to lock endpoint")
			}
			// 4. 更新 Endpoint
			newEndpointActiveAt := time.Now()
			result = tx.Model(&ep).Updates(model.Endpoint{ActiveAt: &newEndpointActiveAt})
			if result.Error != nil {
				return errors.NewErr("failed to update endpoint")
			}
			if result.RowsAffected == 0 {
				return errors.NewErr(fmt.Sprintf("no endpoint found with ID: %d", endpointID))
			}

			return nil
		})

	if err != nil {
		return err
	}

	return nil
}
