package model

import (
	"time"
)

type ReimbursementPromotionalProductsList struct {
	ID                       int        `gorm:"column:id;primaryKey;autoIncrement"`
	SN                       string     `gorm:"column:sn"`                         // 核销单号
	UID                      int        `gorm:"column:uid"`                        // 用户id, 这个id来自admin_users表
	TopAgency                int        `gorm:"column:top_agency"`                 // 一级代理id
	SecondAgency             int        `gorm:"column:second_agency"`              // 二级代理id
	PolicyID                 int        `gorm:"column:policy_id"`                  // 政策id
	Amount                   float64    `gorm:"column:amount"`                     // 申请金额
	ActualAmount             float64    `gorm:"column:actual_amount"`              // 实际核销金额
	ReimbursementApplyAmount float64    `gorm:"column:reimbursement_apply_amount"` // 申请核销金额
	QuantityTotal            int64      `gorm:"column:quantity_total"`             // 数量总计
	CompanyID                int        `gorm:"column:company_id"`                 // 公司id
	Code                     string     `gorm:"column:code"`                       // 客户编码
	Company                  string     `gorm:"column:company"`                    // 公司名称
	Name                     string     `gorm:"column:name"`                       // 姓名
	Phone                    string     `gorm:"column:phone"`                      // 电话
	Province                 int        `gorm:"column:province"`                   // 省
	City                     int        `gorm:"column:city"`                       // 市
	District                 int        `gorm:"column:district"`                   // 区
	Address                  string     `gorm:"column:address"`                    // 地址
	Status                   int        `gorm:"column:status"`                     // 状态
	ReimbursementStatus      int        `gorm:"column:reimbursement_status"`       // 核销状态（客服部）
	CreatedAt                time.Time  `gorm:"column:created_at"`                 // 创建时间
	UpdatedAt                *time.Time `gorm:"column:updated_at"`                 // 更新时间
	AuditTime                *time.Time `gorm:"column:audit_time"`                 // 审批时间
	AuditMan                 *int       `gorm:"column:audit_man"`                  // 申请审批人
	Remark                   *string    `gorm:"column:remark"`                     // 审核不通过原因
	VoucherAuditMan          *int       `gorm:"column:voucher_audit_man"`          // 凭证核销人
	VoucherAuditTime         *time.Time `gorm:"column:voucher_audit_time"`         // 凭证审批时间
	VoucherAuditRemark       *string    `gorm:"column:voucher_audit_remark"`       // 凭证审核不通过原因
	VoucherAuditRollback     int        `gorm:"column:voucher_audit_rollback"`     // 凭证审核是否回退
	DataAuditMan             *int       `gorm:"column:data_audit_man"`             // 材料核销人
	DataVerifyTime           *time.Time `gorm:"column:data_verify_time"`           // 核销材料审批时间
	DataAuditRemark          *string    `gorm:"column:data_audit_remark"`          // 收货单审核不通过原因
	DataAuditRollback        int        `gorm:"column:data_audit_rollback"`        // 收货单审核是否回退
	ReimbursementType        int        `gorm:"column:reimbursement_type"`         // 核销类型
	ExpressGoSn              *string    `gorm:"column:express_go_sn"`              // 快递单号
	ExpressGoCom             *string    `gorm:"column:express_go_com"`             // 快递公司
	ExpressGoTime            *time.Time `gorm:"column:express_go_time"`            // 物流发货时间
	MaterialReturnStatus     int        `gorm:"column:material_return_status"`     // 材料回寄状态
	CompletionStatus         int        `gorm:"column:completion_status"`          // 完成状态
	ExpressComeSn            *string    `gorm:"column:express_come_sn"`            // 物流单号
	ExpressComeCom           *string    `gorm:"column:express_come_com"`           // 物流公司
	ExpressComeTime          *time.Time `gorm:"column:express_come_time"`          // 物流发货时间
	CompletionTime           *time.Time `gorm:"column:completion_time"`            // 完成时间
}

// TableName sets the insert table name for this struct type
func (ReimbursementPromotionalProductsList) TableName() string {
	return "reimbursement_promotional_products_list"
}
