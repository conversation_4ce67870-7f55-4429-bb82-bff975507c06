package dao

import (
	api "marketing/internal/api/machine"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type MachineTypeDao interface {
	EditMachineType(c *gin.Context, id int, uMap map[string]interface{}) error
	UpdateByName(c *gin.Context, name string, uMap map[string]interface{}) error
	GetMachineTypeList(c *gin.Context, name string, pageNum, pageSize int) ([]*model.MachineType, int64)
	GetMachineTypeByModelIds(c *gin.Context, modelIds []int) (list []*model.MachineType)
	GetMachineTypeByModelId(c *gin.Context, modelId int) *model.MachineType
	GetRepairMachineTypeList(c *gin.Context, modelName string, categoryId, visibility, pageNum, pageSize int) ([]*model.RepairMachineType, int64)
	GetModelIDsByCategoryID(c *gin.Context, categoryID int) ([]uint, error)
	GetMachineTypeInfo(c *gin.Context, id int) (*api.MachineTypeInfo, error)
	GetListNoPage(c *gin.Context, req api.MachineTypeReq) ([]*model.MachineType, error)
}

// MachineTypeDaoImpl 实现 MachineTypeDao 接口
type MachineTypeDaoImpl struct {
	db *gorm.DB
}

// NewMachineTypeDao 创建 MachineTypeDao 实例
func NewMachineTypeDao() MachineTypeDao {
	return &MachineTypeDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *MachineTypeDaoImpl) EditMachineType(c *gin.Context, id int, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.MachineType{}).Where("id = ?", id).Updates(uMap).Error
}

func (d *MachineTypeDaoImpl) UpdateByName(c *gin.Context, name string, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.MachineType{}).Where("name = ?", name).Updates(uMap).Error
}

func (d *MachineTypeDaoImpl) GetMachineTypeList(c *gin.Context, name string, pageNum, pageSize int) ([]*model.MachineType, int64) {
	query := d.db.WithContext(c).Model(&model.MachineType{})

	if len(name) > 0 {
		query = query.Where("name like '%" + name + "%'")
	}

	data, total := utils.PaginateQueryV1(query.Order("updated_at desc"), pageNum, pageSize, new([]*model.MachineType))

	return *data, total
}

func (d *MachineTypeDaoImpl) GetMachineTypeByModelIds(c *gin.Context, modelIds []int) (list []*model.MachineType) {
	d.db.WithContext(c).Model(&model.MachineType{}).Where("model_id in (?)", modelIds).Find(&list)
	return
}

func (d *MachineTypeDaoImpl) GetMachineTypeByModelId(c *gin.Context, modelId int) *model.MachineType {
	var m model.MachineType
	err := d.db.WithContext(c).Model(&model.MachineType{}).Where("model_id = ?", modelId).First(&m).Error
	if err != nil {
		return nil
	}
	return &m
}

func (d *MachineTypeDaoImpl) GetModelIDsByCategoryID(c *gin.Context, categoryID int) ([]uint, error) {
	var modelIDs []uint
	err := d.db.WithContext(c).Model(&model.MachineType{}).Where("visibility = 1").
		Where("category_id = ?", categoryID).Pluck("model_id", &modelIDs).Error
	if err != nil {
		return nil, err
	}
	return modelIDs, nil
}

func (d *MachineTypeDaoImpl) GetMachineTypeInfo(c *gin.Context, id int) (*api.MachineTypeInfo, error) {
	var m *api.MachineTypeInfo
	err := d.db.WithContext(c).Table("machine_type as m").
		Joins("left join machine_type_relation as mr on m.name = mr.name").
		Select("m.id,m.name,m.model_id,m.category_id,m.company_price,m.customer_price,m.chart_show,m.prototype_status,m.prototype_apk_path,m.version_code,m.ext_barcode_num,m.visibility,"+
			"mr.declare,mr.stock,mr.delist,mr.delist_on_off_time,is_up_counter,mr.up_counter_time,mr.down_counter_time,mr.market_date,mr.delist_time,mr.discontinued,mr.discontinued_date").
		Where("m.model_id = ?", id).First(&m).Error

	return m, err
}

func (d *MachineTypeDaoImpl) GetListNoPage(c *gin.Context, req api.MachineTypeReq) ([]*model.MachineType, error) {
	var data []*model.MachineType
	query := d.db.WithContext(c).Model(&model.MachineType{})
	if req.Prototype != nil {
		query = query.Where("prototype_status = ?", req.Prototype)
	}

	err := query.Where("created_at > ?", "2024-01-01 00:00:00").Order("model_id desc").Find(&data).Error
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (d *MachineTypeDaoImpl) GetRepairMachineTypeList(c *gin.Context, modelName string, categoryId, visibility, pageNum, pageSize int) ([]*model.RepairMachineType, int64) {
	query := db.GetDB("post_repair").WithContext(c).
		Model(&model.RepairMachineType{}).
		Joins("join machine_category on machine_type.category_id = machine_category.id").
		Select("machine_type.id,model_id,machine_type.name model_name,category_id,machine_category.name category_name,visibility,machine_type.created_at")

	if len(modelName) > 0 {
		query = query.Where("machine_type.name = ?", modelName)
	}

	if categoryId > 0 {
		query = query.Where("category_id = ?", categoryId)
	}

	if visibility >= 0 {
		query = query.Where("visibility = ?", visibility)
	}

	data, total := utils.PaginateQueryV1(query, pageNum, pageSize, new([]*model.RepairMachineType))
	for i, p := range *data {
		(*data)[i].CreateTime = utils.GetTimeStr(p.CreatedAt)
	}

	return *data, total
}
