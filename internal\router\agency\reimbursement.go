package agency

import (
	"marketing/internal/handler/agency/reimbursement"

	"github.com/gin-gonic/gin"
)

type ReimbursementRouter struct {
	reimbursementHandler reimbursement.ReimbursementHandler
}

func NewReimbursementRouter() *ReimbursementRouter {
	reimbursementHandler := reimbursement.NewReimbursement()

	return &ReimbursementRouter{
		reimbursementHandler: reimbursementHandler,
	}
}

func (rr *ReimbursementRouter) Register(r *gin.RouterGroup) {
	// 首页
	r.GET("/homepage", rr.reimbursementHandler.GetHomePage)
}
