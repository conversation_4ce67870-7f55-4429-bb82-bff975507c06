package reimbursement

import (
	"fmt"
	"github.com/gin-gonic/gin"
	api "marketing/internal/api/reimbursement"
	dao "marketing/internal/dao/reimbursement"
	"marketing/internal/pkg/errors"
)

type AdvertService interface {
	// GetAdvertExpenseQuickPickStat 获取广告费用快捷键统计
	GetAdvertExpenseQuickPickStat(c *gin.Context, req *api.AdvertExpenseQuickPickStatReq) (*api.AdvertExpenseQuickPickStatResp, error)
	// GetAdvertExpenseList 获取广告费用列表
	GetAdvertExpenseList(c *gin.Context, req *api.AdvertExpenseListReq) (*api.AdvertExpenseListResp, error)
	// GetAdvertExpenseDetail 获取广告费用详情
	GetAdvertExpenseDetail(c *gin.Context, req *api.OrderReq) (*api.AdvertExpenseDetailResp, error)
	// InvalidAdvertExpense 广告费用申请单作废
	InvalidAdvertExpense(c *gin.Context, orderID int) (bool, error)
	// AuditAdvertExpense 广告费用凭证审核
	AuditAdvertExpense(c *gin.Context, req *api.AuditAdvertExpenseReq) (bool, error)
	// AdvertExpenseOrderSplit 广告费用申请单拆单
	AdvertExpenseOrderSplit(c *gin.Context, req *api.AdvertExpenseOrderSplitReq) error
}

type advert struct {
	repo        dao.ReimbursementRepository
	policyRepo  dao.PolicyRepository
	balanceRepo dao.BalanceRepository
}

func NewAdvertService(repo dao.ReimbursementRepository, policyRepo dao.PolicyRepository, balanceRepo dao.BalanceRepository) AdvertService {
	return &advert{
		repo:        repo,
		policyRepo:  policyRepo,
		balanceRepo: balanceRepo,
	}
}

// GetAdvertExpenseQuickPickStat 获取广告费用快捷键统计
func (a *advert) GetAdvertExpenseQuickPickStat(c *gin.Context, req *api.AdvertExpenseQuickPickStatReq) (*api.AdvertExpenseQuickPickStatResp, error) {
	return a.repo.GetAdvertExpenseQuickPickStat(c, req)
}

// GetAdvertExpenseList 获取广告费用列表
func (a *advert) GetAdvertExpenseList(c *gin.Context, req *api.AdvertExpenseListReq) (*api.AdvertExpenseListResp, error) {
	return a.repo.GetAdvertExpenseList(c, req)
}

// GetAdvertExpenseDetail 获取广告费用详情
func (a *advert) GetAdvertExpenseDetail(c *gin.Context, req *api.OrderReq) (*api.AdvertExpenseDetailResp, error) {
	return a.repo.GetAdvertExpenseDetail(c, req)
}

// InvalidAdvertExpense 广告费用申请单作废
func (a *advert) InvalidAdvertExpense(c *gin.Context, orderID int) (bool, error) {
	// 获取申请单详情
	order, err := a.repo.GetAdvertExpenseOrder(c, orderID, nil)
	if err != nil {
		return false, err
	}

	if order == nil {
		return false, errors.NewErr("未找到此记录信息, 无法作废")
	}

	// 获取关联的政策信息
	policyInfo, err := a.repo.GetPolicyByOrder(c, orderID, 2) // 2表示广告费用类型
	if err != nil {
		return false, err
	}

	if policyInfo == nil {
		return false, errors.NewErr("此政策已过期, 无法作废")
	}

	// 检查政策是否已归档
	if policyInfo.Archive == 1 {
		return false, errors.NewErr("此政策已归档, 无法作废")
	}

	return a.repo.InvalidAdvertExpense(c, orderID)
}

// AuditAdvertExpense 广告费用凭证审核
func (a *advert) AuditAdvertExpense(c *gin.Context, req *api.AuditAdvertExpenseReq) (bool, error) {
	// 获取申请单
	order, err := a.repo.GetAdvertExpenseOrder(c, req.ID, nil)
	if err != nil {
		return false, err
	}
	if order == nil {
		return false, errors.NewErr("申请单不存在")
	}

	// 检查状态是否允许审核
	if order.Status != 0 && order.Status != 1 {
		return false, errors.NewErr("申请单状态不允许审核")
	}

	policyInfo, err := a.repo.GetPolicyByOrder(c, req.ID, 2) // 2表示广告费用类型
	if err != nil {
		return false, err
	}
	if policyInfo == nil {
		return false, errors.NewErr("此政策已过期, 无法审核")
	}

	// 检查政策是否已归档
	if policyInfo.Archive == 1 {
		return false, errors.NewErr("此政策已归档, 无法审核")
	}

	// 执行审核操作
	uid := c.GetUint("uid")
	success, err := a.repo.AuditAdvertExpense(c, req, uid)
	if err != nil {
		return false, err
	}
	if !success {
		return false, errors.NewErr("审核失败，请稍后再试")
	}

	// 推送消息
	msg := ""
	url := fmt.Sprintf("https://h5-yx.readboy.com/manage/reimbursement/record/detail?id=%d&type=advert_expense", req.ID)
	if req.Status == -1 {
		msg = fmt.Sprintf("申请单号%s已打回，请重新填写资料", order.SN)
	} else {
		msg = fmt.Sprintf("申请单号%s需上传收货单材料", order.SN)
	}
	err = reimbursementSMSPush(order.UID, msg, url)
	if err != nil {
		return false, err
	}

	return true, nil
}

// AdvertExpenseOrderSplit 广告费用申请单拆单
func (a *advert) AdvertExpenseOrderSplit(c *gin.Context, req *api.AdvertExpenseOrderSplitReq) error {
	policyInfo, err := a.repo.GetPolicyByOrder(c, req.ID, 3)
	if err != nil {
		return err
	}
	if policyInfo == nil {
		return errors.NewErr("此政策已过期, 无法拆单")
	}
	if policyInfo.Archive == 1 {
		return errors.NewErr("此政策已归档, 无法拆单")
	}
	order, err := a.repo.ReimbursementApplyOrder(c, req.ID)
	if err != nil {
		return err
	}
	if order == nil {
		return errors.NewErr("未找到此申请单，请确认")
	}
	if order.ApplyOrderType != "advert_expense" && order.TurnType != 1 {
		return errors.NewErr("非广告费订单，操作错误")
	}
	if order.Status != 0 {
		return errors.NewErr("申请单状态不允许拆单")
	}
	//统计金额是否相等
	var totalAmount float64
	for _, amount := range req.SplitAmount {
		totalAmount += amount
	}
	if totalAmount != order.Amount {
		return errors.NewErr("拆单金额不等于原订单金额，请检查")
	}
	return a.repo.AdvertExpenseOrderSplit(c, req, order)
}
