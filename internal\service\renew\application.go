package renew

import (
	"errors"
	"fmt"
	"marketing/internal/api/renew"
	"marketing/internal/consts"
	dao "marketing/internal/dao/admin_user"
	endpointDao "marketing/internal/dao/endpoint"
	renewDao "marketing/internal/dao/renew"
	"marketing/internal/dao/warranty"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/mes"
	"marketing/internal/pkg/types"
	"slices"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

type ApplicationServiceInterface interface {
	Add(c *gin.Context, req *renew.AddApplicationReq) error
	Update(c *gin.Context, req *renew.AddApplicationReq) error
	GetMachine(c *gin.Context, barcode string) (any, error)
	GetEndpoint(c *gin.Context) ([]map[string]any, error)
	Audit(c *gin.Context, req *renew.AuditApplicationReq) error
	CheckMachine(c *gin.Context, req *renew.CheckMachineReq) error
	Lists(c *gin.Context, req *renew.ListApplicationReq) ([]*renew.ListApplicationResp, int64, error)
	GetInfo(c *gin.Context, id uint) (*renew.ApplicationInfoResp, error)
	Delete(c *gin.Context, id uint, applicantType string) error
	Completed(c *gin.Context, barcodes []string) (int, error)
	Export(c *gin.Context, req *renew.ListApplicationReq) error
	GetWarranties(c *gin.Context, barcode string) ([]renew.WarrantyResp, error)
}

type application struct {
	userDao     dao.UserDao
	endpointDao endpointDao.EndpointDao
	renewDao    renewDao.ApplicationDao
	warrantyDao warranty.InterfaceWarranty
}

func NewApplicationService(userDao dao.UserDao,
	renewDao renewDao.ApplicationDao,
	endpointDao endpointDao.EndpointDao,
	warrantyDao warranty.InterfaceWarranty,
) ApplicationServiceInterface {
	return &application{
		userDao:     userDao,
		endpointDao: endpointDao,
		renewDao:    renewDao,
		warrantyDao: warrantyDao,
	}
}

// Add 代理申请
func (s *application) Add(c *gin.Context, req *renew.AddApplicationReq) error {
	//先判断条码是否已经存在
	barcode := req.Barcode
	existData, err := s.renewDao.GetByBarcode(c, barcode)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("查询数据失败")
	}
	if existData != nil && existData.ID > 0 {
		return appError.NewErr("该条码已被申请")
	}

	if req.ApplicantType == consts.AgencyPrefix {
		agency, err := s.getAgency(c)
		if err != nil {
			return err
		}
		//业务要求一级申请也要一级审核
		if agency.Level == 1 {
			req.TopAgency = agency.ID
			req.SecondAgency = 0
		} else {
			req.TopAgency = uint(agency.PID)
			req.SecondAgency = agency.ID
		}
	} else {
		endpoint, err := s.getEndpoint(c)
		if err != nil {
			return err
		}
		req.TopAgency = uint(endpoint.TopAgency)
		req.SecondAgency = uint(endpoint.SecondAgency)
		req.EndpointID = uint(endpoint.ID)
	}
	req.Applicant = s.getUid(c)
	err = s.renewDao.Add(c, req)
	return err
}

func (s *application) Update(c *gin.Context, req *renew.AddApplicationReq) error {
	data, err := s.renewDao.Get(c, req.ID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("数据不存在")
	}
	if err != nil {
		return err
	}

	secondUpdateStatus := []string{
		consts.RenewStatusPending,
		consts.RenewStatusRejected,
	}
	if !slices.Contains(secondUpdateStatus, data.Status) {
		return appError.NewErr("只有待审核状态的数据才能修改")
	}
	uid := c.GetUint("uid")
	req.Status = consts.RenewStatusPending

	if req.ApplicantType == consts.AgencyPrefix {

		agency, err := s.getAgency(c)
		if err != nil {
			return err
		}
		if agency.Level == 1 {
			req.TopAgency = agency.ID
			req.SecondAgency = 0
			req.Status = consts.RenewStatusPending
		} else {
			req.TopAgency = uint(agency.PID)
			req.SecondAgency = agency.ID
			req.Status = consts.RenewStatusPending
		}
		if data.TopAgency != req.TopAgency || data.SecondAgency != req.SecondAgency {
			return appError.NewErr("代理商信息不匹配")
		}
	} else {
		endpoint, err := s.getEndpoint(c)
		if err != nil {
			return err
		}
		req.TopAgency = uint(endpoint.TopAgency)
		req.SecondAgency = uint(endpoint.SecondAgency)
		req.EndpointID = uint(endpoint.ID)
		if data.EndpointID != req.EndpointID {
			return appError.NewErr("终端信息不匹配")
		}
	}
	err = s.renewDao.Update(c, req)
	// 驳回重新提交的 记录状态日志
	if data.Status == consts.RenewStatusRejected {
		go func() {
			_ = s.renewDao.UpdateStatus(c, req.ID, uid, req.Status, req.DamageDescription)
		}()
	}
	return err
}

func (s *application) Audit(c *gin.Context, req *renew.AuditApplicationReq) error {
	auditStatus := []string{
		consts.RenewStatusPending,
		consts.RenewStatusSecondLevelReview,
		consts.RenewStatusFirstLevelReview,
	}
	data, err := s.renewDao.Get(c, req.ID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("数据不存在")
	}

	if !slices.Contains(auditStatus, data.Status) {
		return appError.NewErr("当前状态不允许审核")
	}
	if req.AuditType == consts.AgencyPrefix {
		agency, err := s.getAgency(c)
		if err != nil {
			return err
		}
		if agency.ID != data.TopAgency && agency.ID != data.SecondAgency {
			return appError.NewErr("您无权审核")
		}
		if req.Status == "passed" {
			if agency.Level == 1 {
				req.Status = consts.RenewStatusFirstLevelReview
			} else {
				req.Status = consts.RenewStatusSecondLevelReview
			}
		} else if req.Status == "rejected" {
			req.Status = consts.RenewStatusRejected
		}
	} else {
		if data.Status == consts.RenewStatusPending {
			return appError.NewErr("总代未审核")
		}
	}
	uid := s.getUid(c)
	req.HandlerID = uid
	err = s.renewDao.Audit(c, req)
	return err
}

func (s *application) CheckMachine(c *gin.Context, req *renew.CheckMachineReq) error {
	data, err := s.renewDao.Get(c, req.ID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("数据不存在")
	}
	if err != nil {
		return err
	}
	if data.Status != consts.RenewStatusHeadOfficeReview && data.Status != consts.RenewStatusRepair {
		return appError.NewErr("当前状态不允许检测")
	}
	uid := s.getUid(c)
	req.HandlerID = uid
	req.Comment = req.CheckResult
	req.Status = consts.RenewStatusRepair
	err = s.renewDao.Check(c, req)
	return err
}

func (s *application) Lists(c *gin.Context, req *renew.ListApplicationReq) ([]*renew.ListApplicationResp, int64, error) {
	systemType := c.GetString("system_type")
	if systemType == consts.AgencyPrefix {
		agency, err := s.getAgency(c)
		if err != nil {
			return nil, 0, err
		}
		if agency.ID == 0 {
			return nil, 0, appError.NewErr("未找到代理商信息")
		}
		//dataType1 经销商审核列表
		//dataType2 经销商申请列表
		if req.DataType == 1 {
			req.AgencyID = agency.ID
			req.Applicant = s.getUid(c)
		} else {
			req.Applicant = s.getUid(c)
		}
	} else if systemType == "renew-endpoint" {
		endpoint, err := s.getEndpoint(c)
		if err != nil {
			return nil, 0, err
		}
		req.EndpointID = uint(endpoint.ID)
		req.ApplicantType = consts.EndpointPrefix
	}
	data, total, err := s.renewDao.Lists(c, req)
	if err != nil {
		return nil, 0, appError.NewErr("查询数据失败").WithStack()
	}

	// Collect all application IDs to fetch status logs
	var applicationIDs []uint
	for _, item := range data {
		applicationIDs = append(applicationIDs, uint(item.ID))
	}

	// Get status logs for all applications in a single batch query
	var statusLogsMap map[uint][]*model.RenewApplicationStatus
	if len(applicationIDs) > 0 {
		var err error
		statusLogsMap, err = s.renewDao.GetStatusBatch(c, applicationIDs)
		if err != nil {
			// Log the error but continue with empty status logs
			statusLogsMap = make(map[uint][]*model.RenewApplicationStatus)
		}
	} else {
		statusLogsMap = make(map[uint][]*model.RenewApplicationStatus)
	}

	var userIDs []uint
	var agencyIDs, endpointIDs []int
	for _, item := range data {
		userIDs = append(userIDs, item.Applicant)
		if item.TopAgency != 0 {
			agencyIDs = append(agencyIDs, int(item.TopAgency))
		}
		if item.SecondAgency != 0 {
			agencyIDs = append(agencyIDs, int(item.SecondAgency))
		}
		if item.EndpointID != 0 {
			endpointIDs = append(endpointIDs, int(item.EndpointID))
		}
	}

	// Collect handler IDs from status logs
	for _, logs := range statusLogsMap {
		for _, log := range logs {
			userIDs = append(userIDs, log.HandlerID)
		}
	}

	users, _ := s.userDao.GetMapByIDs(c, userIDs)
	agencies, _ := s.endpointDao.GetAgencies(c, agencyIDs)
	endpoints, err := s.endpointDao.GetEndpointMap(c, endpointIDs)
	var res []*renew.ListApplicationResp
	RenewStatusMap := consts.GetRenewStatus()
	NextStatusSlices := consts.GetRenewNextStatusMap()

	for _, item := range data {
		listResp := &renew.ListApplicationResp{
			RenewApplication: *item,
			StatusName:       RenewStatusMap[item.Status],
			ApplicantName:    users[item.Applicant].Name,
			IssuesSlices:     strings.Split(item.Issues, ","),
			TopAgencyName:    agencies[item.TopAgency],
			SecondAgencyName: agencies[item.SecondAgency],
			EndpointName:     endpoints[item.EndpointID],
			NextStatusName:   NextStatusSlices[item.Status],
		}

		// Add audit logs to each item
		if statusLogs, ok := statusLogsMap[uint(item.ID)]; ok {
			for _, log := range statusLogs {
				listResp.Audits = append(listResp.Audits, renew.AuditResp{
					RenewApplicationStatus: *log,
					StatusName:             RenewStatusMap[log.Status],
					HandlerName:            users[log.HandlerID].Name,
				})
			}
		}

		res = append(res, listResp)
	}

	return res, total, err
}

func (s *application) GetInfo(c *gin.Context, id uint) (*renew.ApplicationInfoResp, error) {
	var res *renew.ApplicationInfoResp
	info, err := s.renewDao.Get(c, id)
	if err != nil {
		return nil, err
	}
	statusLog, err := s.renewDao.GetStatus(c, id)
	if err != nil {
		return nil, err
	}
	userIDs := []uint{info.Applicant}
	for _, item := range statusLog {
		userIDs = append(userIDs, item.HandlerID)
	}
	users, _ := s.userDao.GetMapByIDs(c, userIDs)
	RenewStatusMap := consts.GetRenewStatus()

	var agencyIDs, endpointIDs []int
	if info.TopAgency != 0 {
		agencyIDs = append(agencyIDs, int(info.TopAgency))
	}
	if info.SecondAgency != 0 {
		agencyIDs = append(agencyIDs, int(info.SecondAgency))
	}
	if info.EndpointID != 0 {
		endpointIDs = append(endpointIDs, int(info.EndpointID))
	}
	agencies, _ := s.endpointDao.GetAgencies(c, agencyIDs)
	endpoints, err := s.endpointDao.GetEndpointMap(c, endpointIDs)
	NextStatusSlices := consts.GetRenewNextStatusMap()

	res = &renew.ApplicationInfoResp{
		RenewApplication: *info,
		StatusName:       RenewStatusMap[info.Status],
		ApplicantName:    users[info.Applicant].Name,
		IssuesSlices:     strings.Split(info.Issues, ","),
		TopAgencyName:    agencies[info.TopAgency],
		SecondAgencyName: agencies[info.SecondAgency],
		EndpointName:     endpoints[info.EndpointID],
		NextStatusName:   NextStatusSlices[info.Status],
	}
	RenewStatusMap["Pending"] = "重新提交"
	for _, item := range statusLog {
		res.Audits = append(res.Audits, renew.AuditResp{
			RenewApplicationStatus: *item,
			StatusName:             RenewStatusMap[item.Status],
			HandlerName:            users[item.HandlerID].Name,
		})
	}
	return res, err
}

func (s *application) Delete(c *gin.Context, id uint, applicantType string) error {
	data, err := s.renewDao.Get(c, id)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("数据不存在")
	}
	if err != nil {
		return err
	}
	if applicantType == consts.AgencyPrefix {
		agency, err := s.getAgency(c)
		if err != nil {
			return err
		}
		if agency.Level == 1 && data.TopAgency != agency.ID {
			return appError.NewErr("您无权删除")
		} else if agency.Level == 2 && data.SecondAgency != agency.ID {
			return appError.NewErr("您无权删除")
		}
	} else {
		endpoint, err := s.getEndpoint(c)
		if err != nil {
			return err
		}
		if data.EndpointID != uint(endpoint.ID) {
			return appError.NewErr("您无权删除")
		}
	}
	return s.renewDao.Delete(c, id)
}

func (s *application) GetMachine(c *gin.Context, barcode string) (any, error) {
	var req mes.CheckMachineParams
	req.Barcode = barcode
	data, err := mes.CheckMachine(req)
	if err != nil {
		return nil, appError.NewErr(err.Error())
	}
	return data, err
}

func (s *application) GetEndpoint(c *gin.Context) ([]map[string]any, error) {
	uid := s.getUid(c)
	agency, err := s.userDao.UserAgency(c, uid)
	if err != nil {
		return nil, err
	}
	if agency == nil {
		return nil, appError.NewErr("用户未绑定代理商")
	}
	agencyId := agency.ID
	endpoints, err := s.endpointDao.GetEndpointByAgency(c, agencyId, 1)
	if err != nil {
		return nil, err
	}
	if endpoints == nil {
		return nil, appError.NewErr("没有可用的终端")
	}
	var data []map[string]any
	for _, endpoint := range *endpoints {
		data = append(data, map[string]any{
			"id":   endpoint.ID,
			"name": endpoint.Name,
		})
	}
	return data, nil
}

func (s *application) Completed(c *gin.Context, barcodes []string) (int, error) {
	// 查出所有的条码信息
	data, err := s.renewDao.GetByBarcodes(c, barcodes)
	if err != nil {
		return 0, err
	}
	if data == nil {
		return 0, appError.NewErr("没有找到相关数据")
	}
	var ids []uint
	for _, item := range data {
		ids = append(ids, cast.ToUint(item.ID))
	}
	err = s.renewDao.Completed(c, ids, s.getUid(c))
	return len(ids), err
}

func (s *application) Export(c *gin.Context, req *renew.ListApplicationReq) error {
	systemType := c.GetString("system_type")

	// 调用 Lists 方法获取数据
	req.PageSize = 100000
	req.DataType = 1
	data, _, err := s.Lists(c, req)
	if err != nil {
		return err
	}

	// 创建一个新的 Excel 文件
	f := excelize.NewFile()
	sheetName := "Sheet1"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return err
	}
	f.SetActiveSheet(index)

	// 设置表头
	var headers []string
	if systemType == consts.AgencyPrefix {
		headers = []string{
			"终端", "SN码", "机型", "审核状态", "申请人", "故障", "故障描述", "实测结果", "外观检查", "申请时间", "审核日志",
		}
	} else {
		headers = []string{
			"区域", "SN码", "机型", "审核状态", "申请人", "故障", "故障描述", "实测结果", "外观检查", "申请时间", "审核日志",
		}
	}

	// 创建标题行样式
	titleStyle, err := f.NewStyle(&excelize.Style{
		Font:      &excelize.Font{Color: "1f7f3b", Bold: true, Family: "Microsoft YaHei"},
		Fill:      excelize.Fill{Type: "pattern", Color: []string{"E6F4EA"}, Pattern: 1},
		Alignment: &excelize.Alignment{Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Style: 2, Color: "1f7f3b"},
			{Type: "bottom", Style: 1, Color: "1f7f3b"},
			{Type: "left", Style: 1, Color: "1f7f3b"},
		},
	})
	if err != nil {
		return err
	}

	// 设置标题行内容
	if err = f.SetSheetRow(sheetName, "A1", &headers); err != nil {
		return err
	}

	// 设置标题行行高
	if err = f.SetRowHeight(sheetName, 1, 30); err != nil {
		return err
	}

	// 设置标题行单元格样式
	if err = f.SetCellStyle(sheetName, "A1", "K1", titleStyle); err != nil {
		return err
	}

	// 设置列宽
	if err = f.SetColWidth(sheetName, "A", "J", 20); err != nil {
		return err
	}

	// 设置审核日志列宽度更宽
	if err = f.SetColWidth(sheetName, "K", "K", 40); err != nil {
		return err
	}

	// 填充数据
	for rowIndex, item := range data {
		var tempName string
		if systemType == consts.AgencyPrefix {
			tempName = item.EndpointName
		} else {
			tempName = item.TopAgencyName
		}

		// 格式化审核日志
		var auditLogs string
		if len(item.Audits) > 0 {
			var logEntries []string
			for _, audit := range item.Audits {
				// Convert CustomTime to time.Time for formatting
				logEntry := fmt.Sprintf("%s - %s - %s",
					audit.HandlerName,
					audit.StatusName,
					audit.Comment)
				logEntries = append(logEntries, logEntry)
			}
			auditLogs = strings.Join(logEntries, "\n")
		} else {
			auditLogs = "无审核记录"
		}

		row := []interface{}{
			tempName,
			item.RenewApplication.Barcode,
			item.RenewApplication.Model,
			item.StatusName,
			item.ApplicantName,
			item.RenewApplication.Issues,
			item.RenewApplication.DamageDescription,
			item.RenewApplication.CheckResult,
			item.RenewApplication.CheckFacade,
			item.RenewApplication.CreatedAt,
			auditLogs,
		}
		for colIndex, value := range row {
			cell, err := excelize.CoordinatesToCellName(colIndex+1, rowIndex+2)
			if err != nil {
				return err
			}

			// 如果是审核日志列，设置自动换行
			if colIndex == 10 {
				style, err := f.NewStyle(&excelize.Style{
					Alignment: &excelize.Alignment{
						WrapText: true,
						Vertical: "top",
					},
				})
				if err != nil {
					return err
				}
				if err := f.SetCellStyle(sheetName, cell, cell, style); err != nil {
					return err
				}

				// 设置行高以适应多行内容
				if len(item.Audits) > 0 {
					rowHeight := 15.0 * float64(len(item.Audits))
					if rowHeight < 20 {
						rowHeight = 20
					}
					if err := f.SetRowHeight(sheetName, rowIndex+2, rowHeight); err != nil {
						return err
					}
				}
			}

			err = f.SetCellValue(sheetName, cell, value)
			if err != nil {
				return err
			}
		}
	}

	// 设置响应头
	fileName := fmt.Sprintf("换新申请_%s.xlsx", time.Now().Format("20060102_150405"))
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")

	// 直接将 Excel 文件内容写入响应
	if err = f.Write(c.Writer); err != nil {
		return err
	}

	return nil
}

// GetWarranties 代理查询保卡
func (s *application) GetWarranties(c *gin.Context, barcode string) ([]renew.WarrantyResp, error) {
	var param warranty.ListParams
	param.Barcode = barcode
	systemType := c.GetString("system_type")
	if systemType == consts.AgencyPrefix {
		agency, _ := s.getAgency(c)

		if agency != nil {
			param.AgencyID = agency.ID
		}
	} else if systemType == "renew-endpoint" {
		endpoint, err := s.getEndpoint(c)
		if err != nil {
			return nil, err
		}
		param.EndpointID = uint(endpoint.ID)
	}
	warranties, err := s.warrantyDao.GetWarranties(c, &param)
	if err != nil {
		return nil, appError.NewErr("查询数据失败").WithStack()
	}
	if len(warranties) == 0 {
		return nil, appError.NewErr("没有找到相关保卡信息")
	}
	// 终端名称
	var endpointIDs []int
	for _, item := range warranties {
		if item.Endpoint != 0 {
			endpointIDs = append(endpointIDs, item.Endpoint)
		}
	}
	endpoints, err := s.endpointDao.GetEndpointMap(c, endpointIDs)
	if err != nil {
		return nil, appError.NewErr("查询终端信息失败").WithStack()
	}

	var resp []renew.WarrantyResp
	for _, w := range warranties {
		resp = append(resp, renew.WarrantyResp{
			Barcode:        w.Barcode,
			ModelID:        w.ModelID,
			Model:          w.Model,
			BuyDate:        types.CustomTime(w.BuyDate),
			Status:         w.Status,
			ActivatedAtOld: types.CustomTime(w.ActivatedAtOld),
			CustomerName:   w.CustomerName,
			CustomerPhone:  w.CustomerPhone,
			Endpoint:       endpoints[uint(w.Endpoint)],
		})
	}
	return resp, nil
}

func (s *application) getAgency(c *gin.Context) (*model.Agency, error) {
	//判断用户是总代还是二代
	uid := c.GetUint("uid")

	agency, err := s.userDao.UserAgency(c, uint(uid))
	if err != nil {
		return nil, err
	}
	if agency == nil || agency.ID == 0 {
		return nil, appError.NewErr("用户未绑定代理商")
	}
	return agency, nil
}

func (s *application) getUid(c *gin.Context) uint {
	uid := c.GetUint("uid")

	return uid
}

func (s *application) getEndpoint(c *gin.Context) (*model.Endpoint, error) {
	uid := c.GetUint("uid")
	endpoint, err := s.userDao.UserEndpoint(c, uid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appError.NewErr("用户未绑定终端")
		}
		return nil, err
	}
	return endpoint, nil
}
