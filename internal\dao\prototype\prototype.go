package prototype

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	api "marketing/internal/api/prototype"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
)

type ListParams struct {
	ModelID        []uint
	Status         *int
	CreatedAtStart string
	CreatedAtEnd   string
	Page           int
	PageSize       int
	TopAgency      uint
	SecondAgency   uint
	Endpoint       uint
	Type           *int
	Barcode        string
}

type PrototypeStat struct {
	ModelID              uint
	Model                string
	TopAgency            uint
	PrototypeCount       int64
	EndpointCount        int64
	InitialEndpointCount int64
	RenewCount           int64
}

var showType = []int{0, 1, 4}

// PrototypeInterface  样机表接口
type PrototypeInterface interface {
	GetList(c *gin.Context, param *ListParams) ([]*model.Prototype, int64, error)
	GetListWithAgency(c *gin.Context, param *ListParams) ([]*model.Prototype, int64, error)
	GetByBarcode(c *gin.Context, barcode string, status int) (*model.Prototype, error)
	Update(c *gin.Context, id int, pt map[string]any) error
	PrototypeStatList(c *gin.Context, param *ListParams) ([]*PrototypeStat, int64, error)
	GetPrototypeModelList(c *gin.Context) ([]string, error)
	DB() *gorm.DB

	GetInfo(c *gin.Context, barcode string) (*api.WithMTAndPC, error)
	PrototypeCheck(c *gin.Context, barcode, number string) (int64, error)
}

type Prototype struct {
	db *gorm.DB
}

func NewPrototypeDao(db *gorm.DB) PrototypeInterface {
	return &Prototype{
		db: db,
	}
}

func (p *Prototype) GetList(c *gin.Context, param *ListParams) ([]*model.Prototype, int64, error) {
	var list []*model.Prototype
	var count int64
	query := p.db.WithContext(c).Model(&model.Prototype{}).Unscoped()
	query = query.Where("type IN (?)", showType)
	if len(param.ModelID) > 0 {
		query = query.Where("model_id IN (?)", param.ModelID)
	}
	if param.Status != nil {
		query = query.Where("status = ?", *param.Status)
	}
	if param.CreatedAtStart != "" {
		query = query.Where("created_at >= ?", param.CreatedAtStart)
	}
	if param.CreatedAtEnd != "" {
		query = query.Where("created_at <= ?", param.CreatedAtEnd)
	}
	if param.Type != nil {
		query = query.Where("type = ?", *param.Type)
	}
	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, appError.NewErr(err.Error()).WithError(err)
	}
	// 分页
	offset := (param.Page - 1) * param.PageSize
	query = query.Offset(offset).Limit(param.PageSize)

	err = query.Find(&list).Error
	if err != nil {
		return nil, 0, appError.NewErr(err.Error()).WithError(err)
	}
	return list, count, nil
}

func (p *Prototype) GetListWithAgency(c *gin.Context, param *ListParams) ([]*model.Prototype, int64, error) {
	var list []*model.Prototype
	var count int64
	query := p.db.WithContext(c).Table("prototype").Unscoped()
	query = query.Joins("LEFT JOIN agency ta ON prototype.top_agency = ta.id")
	query = query.Joins("LEFT JOIN agency sa ON prototype.second_agency = sa.id")
	query = query.Joins("LEFT JOIN endpoint e ON prototype.endpoint = e.id")
	query = query.Where("prototype.type IN (?)", showType)

	if param.TopAgency != 0 {
		query = query.Where("prototype.top_agency = ? or e.top_agency = ?", param.TopAgency, param.TopAgency)
	}
	if param.SecondAgency != 0 {
		query = query.Where("prototype.second_agency = ? or e.second_agency = ?", param.SecondAgency, param.SecondAgency)
	}
	if param.Endpoint != 0 {
		query = query.Where("prototype.endpoint = ?", param.Endpoint)
	}
	if param.Type != nil {
		query = query.Where("prototype.type = ?", *param.Type)
	}
	if param.Status != nil {
		// 1-在库 其他-离库
		if *param.Status == 1 {
			query = query.Where("prototype.status = ?", *param.Status)
		} else {
			query = query.Where("prototype.status != ?", 1)
		}
	}
	if param.CreatedAtStart != "" {
		query = query.Where("prototype.created_at >= ?", param.CreatedAtStart)
	}
	if param.CreatedAtEnd != "" {
		query = query.Where("prototype.created_at <= ?", param.CreatedAtEnd)
	}
	if param.Barcode != "" {
		query = query.Where("prototype.barcode = ?", param.Barcode)
	}
	if param.ModelID != nil {
		query = query.Where("prototype.model_id IN (?)", param.ModelID)
	}

	var fields = []string{
		"prototype.*",
		"IF(prototype.top_agency = 0, e.top_agency, prototype.top_agency) as top_agency",
		"IF(prototype.second_agency = 0, e.second_agency, prototype.second_agency) as second_agency",
	}
	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}
	// 分页
	offset := (param.Page - 1) * param.PageSize
	query = query.Offset(offset).Limit(param.PageSize)

	err = query.Select(fields).Order("prototype.created_at DESC").Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (p *Prototype) GetByBarcode(c *gin.Context, barcode string, status int) (*model.Prototype, error) {
	var prototype model.Prototype
	err := p.db.WithContext(c).Where("barcode = ? AND status = ?", barcode, status).First(&prototype).Error
	if err != nil {
		return nil, err
	}
	return &prototype, nil
}

func (p *Prototype) Update(c *gin.Context, id int, pt map[string]any) error {
	return p.db.WithContext(c).Model(&model.Prototype{}).Where("id = ?", id).Updates(pt).Error
}

func (p *Prototype) PrototypeStatList(c *gin.Context, param *ListParams) ([]*PrototypeStat, int64, error) {
	var prototype []*PrototypeStat
	var count int64
	var fields = []string{
		"p.model_id",
		"m.name as model",
		"IF(p.top_agency = 0, e.top_agency, p.top_agency) as top_agency",
		"COUNT(*) as prototype_count",
	}
	query := p.db.WithContext(c).Table("prototype as p").Unscoped()
	query = query.Joins("LEFT JOIN endpoint e ON p.endpoint = e.id")
	query = query.Joins("LEFT JOIN machine_type m ON m.model_id = p.model_id")
	query = query.Where("p.type IN (?)", showType)
	query = query.Where("p.status = ?", 1)
	query = query.Select(fields)

	if param.TopAgency != 0 {
		query = query.Where("IF(p.top_agency = 0, e.top_agency, p.top_agency) = ?", param.TopAgency)
	}
	if param.ModelID != nil {
		query = query.Where("p.model_id IN (?)", param.ModelID)
	}
	if param.Type != nil {
		query = query.Where("p.type = ?", param.Type)
	}

	query = query.Group("p.model_id, IF(p.top_agency = 0, e.top_agency, p.top_agency)")
	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, appError.NewErr(err.Error()).WithError(err)
	}
	// 分页
	offset := (param.Page - 1) * param.PageSize
	query = query.Offset(offset).Limit(param.PageSize)
	err = query.Scan(&prototype).Error
	if err != nil {
		return nil, 0, appError.NewErr(err.Error()).WithError(err)
	}
	// 获取换新数量
	var renewCount []model.PrototypeRenew
	err = p.db.WithContext(c).Model(&model.PrototypeRenew{}).Scan(&renewCount).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, 0, appError.NewErr(err.Error()).WithError(err)
	}
	// 获取初始数量
	var initialCount []model.PrototypeUpEndpointNumber
	err = p.db.WithContext(c).Model(&model.PrototypeUpEndpointNumber{}).Scan(&initialCount).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, count, appError.NewErr(err.Error()).WithError(err)
	}
	// 处理数据
	for _, v := range prototype {
		for _, r := range renewCount {
			if v.Model == r.Model && v.TopAgency == r.TopAgency {
				v.RenewCount = r.Amount
				break
			}
		}
		for _, i := range initialCount {
			if v.Model == i.Model && v.TopAgency == i.TopAgency {
				v.InitialEndpointCount = i.EndpointNumber
				break
			}
		}
	}

	return prototype, count, nil
}
func (p *Prototype) GetPrototypeModelList(c *gin.Context) ([]string, error) {
	var list []string
	err := p.db.WithContext(c).Model(&model.MachineType{}).Distinct("name").Where("prototype_status =?", 1).Order("model_id desc").Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

// DB 返回数据库连接
func (p *Prototype) DB() *gorm.DB {
	return p.db
}

func (p *Prototype) GetInfo(c *gin.Context, barcode string) (*api.WithMTAndPC, error) {
	var resp api.WithMTAndPC
	err := p.db.WithContext(c).
		Table("prototype as p").
		Select(`p.number, p.status, p.type, p.endpoint, p.top_agency, p.second_agency, mt.category_id, 
            COALESCE(pc.discontinued, 0) AS discontinued`).
		Joins("LEFT JOIN machine_type mt ON p.model_id = mt.model_id").
		Joins("LEFT JOIN prototype_config pc ON p.model_id = pc.model_id").
		Where("p.barcode = ? AND p.status = 1", barcode).
		First(&resp).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &resp, nil
}

func (p *Prototype) PrototypeCheck(c *gin.Context, barcode, number string) (int64, error) {
	var count int64
	query := p.db.WithContext(c).
		Table("prototype").
		Where("status = ?", 1)

	if barcode != "" {
		query = query.Where("barcode = ?", barcode)
	} else {
		query = query.Where("number = ?", number)
	}
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
