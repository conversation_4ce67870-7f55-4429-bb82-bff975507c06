package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

type KingDeeAgencyDao interface {
	GetKingDeeAgencyList(c *gin.Context, ids []int) (list []*model.KingDeeAgency)
	GetKingDeeAgency(c *gin.Context, code string) (*model.KingDeeAgencyInfo, error)
}

// KingDeeAgencyDaoImpl 实现 KingDeeAgencyDao 接口
type KingDeeAgencyDaoImpl struct {
	db *gorm.DB
}

// NewKingDeeAgencyDao 创建 KingDeeAgencyDao 实例
func NewKingDeeAgencyDao() KingDeeAgencyDao {
	return &KingDeeAgencyDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *KingDeeAgencyDaoImpl) GetKingDeeAgencyList(c *gin.Context, ids []int) (list []*model.KingDeeAgency) {
	d.db.WithContext(c).Model(&model.KingDeeAgency{}).Where("id in (?)", ids).Find(&list)
	return
}

func (d *KingDeeAgencyDaoImpl) GetKingDeeAgency(c *gin.Context, code string) (*model.KingDeeAgencyInfo, error) {
	var info model.KingDeeAgencyInfo
	err := d.db.WithContext(c).Table("kingdee_agency as ka").
		Select("ka.id, ka.name AS company_name, ka.code, a.id AS top_agency, a.name AS agency_name, a.level").
		Joins("LEFT JOIN agency_kingdee ak ON ka.id = ak.kingdee_id").
		Joins("LEFT JOIN agency a ON ak.agency_id = a.id").
		Where("ka.code = ?", code).
		First(&info).Error
	if err != nil {
		return nil, err
	}
	return &info, nil
}
