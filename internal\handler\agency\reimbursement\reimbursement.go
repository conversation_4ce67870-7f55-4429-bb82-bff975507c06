package reimbursement

import (
	api "marketing/internal/api/reimbursement"
	"marketing/internal/dao/admin_user"
	dao "marketing/internal/dao/reimbursement"
	"marketing/internal/handler"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/errors"
	service "marketing/internal/service/reimbursement"

	"github.com/gin-gonic/gin"
)

// ReimbursementHandler handles reimbursement related requests for agency
type ReimbursementHandler interface {
	// GetHomePage 获取报销首页数据
	GetHomePage(c *gin.Context)
	// GetClientSummary 获取客户端汇总数据
	GetClientSummary(c *gin.Context)
}

type reimbursement struct {
	homePageService service.HomePageService
}

// NewReimbursement creates a new ReimbursementHandler for agency
func NewReimbursement() ReimbursementHandler {
	Db := db.GetDB()
	reimbursementRepo := dao.NewReimbursementRepository(Db)
	userRepo := admin_user.NewUserDao(Db)
	balanceRepo := dao.NewBalanceRepository(Db)
	homePageService := service.NewHomePageService(reimbursementRepo, userRepo, balanceRepo)

	return &reimbursement{
		homePageService: homePageService,
	}
}

// GetHomePage 获取报销首页数据
func (r *reimbursement) GetHomePage(c *gin.Context) {
	var req api.HomePageReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 调用服务获取首页数据
	resp, err := r.homePageService.GetHomePage(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, resp)
}

// GetClientSummary 获取客户端汇总数据
func (r *reimbursement) GetClientSummary(c *gin.Context) {
	var req api.ClientSummaryReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 调用服务获取汇总数据
	resp, err := r.homePageService.GetClientSummary(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, resp)
}
