package reimbursement

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	api "marketing/internal/api/reimbursement"
	dao "marketing/internal/dao/reimbursement"
	"marketing/internal/pkg/errors"
	"strconv"
	"time"
)

type ReimbursementService interface {
	//ReimbursementPolicySummary 核销单汇总
	ReimbursementPolicySummary(c *gin.Context, req *api.PolicySummaryReq) ([]*api.ReimbursementSummaryGroupByPolicyResp, int64, error)
	// MaterialSignIn 材料签收
	MaterialSignIn(c *gin.Context, orderID int, orderType string) (bool, error)
	// GetSummaryShortcutStat 获取核销小单列表快捷键统计
	GetSummaryShortcutStat(c *gin.Context, req *api.SummaryShortcutStatReq) (*api.ShortcutStatResp, error)
	// GetSummaryList 获取核销小单列表
	GetSummaryList(c *gin.Context, req *api.SummaryShortcutStatReq) ([]*api.SummaryListItem, int64, error)
	// ApplyOrderSummaryInvalid 核销小单作废
	ApplyOrderSummaryInvalid(c *gin.Context, req *api.ApplyOrderSummaryInvalidReq) error
	//ReimbursementSubmit 核销提交
	ReimbursementSubmit(c *gin.Context, req *api.ApplyOrderSummarySubmitReq) error
	// ReimbursementRetrial 核销重审
	ReimbursementRetrial(c *gin.Context, req *api.ReimbursementRetrialReq) error
	//ReimbursementAudit 核销入账
	ReimbursementAudit(c *gin.Context, req *api.ReimbursementAuditReq) error
	//ReimbursementCompanySummary 核销单按公司汇总列表
	ReimbursementCompanySummary(c *gin.Context, req *api.CompanySummaryReq) (*api.CompanySummaryStatResp, []*api.ReimbursementSummaryPageResp, int64, error)
}

type reimbursement struct {
	repo        dao.ReimbursementRepository
	policyRepo  dao.PolicyRepository
	balanceRepo dao.BalanceRepository
}

func NewReimbursementService(repo dao.ReimbursementRepository, policyRepo dao.PolicyRepository, balanceRepo dao.BalanceRepository) ReimbursementService {
	return &reimbursement{
		repo:        repo,
		policyRepo:  policyRepo,
		balanceRepo: balanceRepo,
	}
}

// H5URL 推送消息的H5地址结构
type H5URL struct {
	Name  string `json:"name"`
	URL   string `json:"url"`
	Icon  string `json:"icon"`
	Index string `json:"index"`
	Key   string `json:"key"`
}

// PushBody 推送消息体结构
type PushBody struct {
	Platform []string `json:"platform"`
	Audience Audience `json:"audience"`
	Title    string   `json:"title"`
	Content  string   `json:"content"`
	Slug     string   `json:"slug"`
	Action   string   `json:"action"`
	URL      string   `json:"url"`
}

// Audience 推送受众结构
type Audience struct {
	Users []string `json:"users"`
}

func (a *reimbursement) ReimbursementPolicySummary(c *gin.Context, req *api.PolicySummaryReq) ([]*api.ReimbursementSummaryGroupByPolicyResp, int64, error) {
	//获取政策再统计
	policyParam := &api.PolicyListSearch{
		Archive: req.Archive,
	}
	policyParam.PageSize = req.PageSize
	policyParam.Page = req.Page
	polices, total, err := a.policyRepo.GetPolicyList(policyParam)
	if err != nil {
		return nil, 0, errors.NewErr("获取政策列表失败: " + err.Error())
	}
	data, err := a.repo.ReimbursementSummaryGroupByPolicy(c, polices)
	if err != nil {
		return nil, 0, errors.NewErr("获取核销单汇总失败: " + err.Error())
	}
	return data, total, nil
}

func (a *reimbursement) ReimbursementCompanySummary(c *gin.Context, req *api.CompanySummaryReq) (*api.CompanySummaryStatResp, []*api.ReimbursementSummaryPageResp, int64, error) {
	policyInfo, err := a.policyRepo.GetPolicyByID(req.PolicyID)
	if err != nil {
		return nil, nil, 0, err
	}
	if policyInfo == nil {
		return nil, nil, 0, errors.NewErr("未找到核销政策信息")
	}
	req.PolicyType = policyInfo.PolicyType
	req.StandardType = policyInfo.StandardType
	summaryStat, err := a.repo.GetReimbursementAllSummaryTotal(c, req)
	if err != nil {
		return nil, nil, 0, errors.NewErr("获取核销小单统计失败: " + err.Error())
	}
	data, total, err := a.repo.GetReimbursementAllSummaryPage(c, req)
	return summaryStat, data, total, err
}

// MaterialSignIn 材料签收
func (a *reimbursement) MaterialSignIn(c *gin.Context, orderID int, orderType string) (bool, error) {
	// 获取申请单详情
	var materialReturnStatus int
	if orderType == "promotional_products" {
		order, err := a.repo.GetPromotionalProductsOrder(c, orderID, nil)
		if err != nil {
			return false, err
		}
		if order == nil {
			return false, errors.NewErr("未找到此申请单，请确认")
		}
		materialReturnStatus = order.MaterialReturnStatus
	} else {
		order, err := a.repo.GetAdvertExpenseOrder(c, orderID, nil)
		if err != nil {
			return false, err
		}
		if order == nil {
			return false, errors.NewErr("未找到此申请单，请确认")
		}
		materialReturnStatus = order.MaterialReturnStatus
	}

	// 检查材料回寄状态
	if materialReturnStatus == 2 {
		return false, errors.NewErr("材料已签收，无需二次操作")
	}
	return a.repo.MaterialSignIn(c, orderID, orderType)
}

func (a *reimbursement) GetSummaryShortcutStat(c *gin.Context, req *api.SummaryShortcutStatReq) (*api.ShortcutStatResp, error) {
	// 获取核销小单列表快捷键统计
	//获取核销政策
	policyInfo, err := a.policyRepo.GetPolicyByID(req.PolicyID)
	if err != nil {
		return nil, err
	}
	if policyInfo == nil {
		return nil, errors.NewErr("未找到核销政策信息")
	}
	return a.repo.GetSummaryShortcutStat(c, req, policyInfo.StandardType)
}

func (a *reimbursement) GetSummaryList(c *gin.Context, req *api.SummaryShortcutStatReq) ([]*api.SummaryListItem, int64, error) {
	policyInfo, err := a.policyRepo.GetPolicyByID(req.PolicyID)
	if err != nil {
		return nil, 0, err
	}
	if policyInfo == nil {
		return nil, 0, errors.NewErr("未找到核销政策信息")
	}
	req.PolicyType = policyInfo.PolicyType
	return a.repo.GetSummaryList(c, req)
}

// ApplyOrderSummaryInvalid 核销小单作废
func (a *reimbursement) ApplyOrderSummaryInvalid(c *gin.Context, req *api.ApplyOrderSummaryInvalidReq) error {
	// 获取核销小单详情

	policyInfo, err := a.repo.GetPolicyByOrder(c, req.ID, 3) // 3表示核销小单类型
	if err != nil {
		return err
	}
	if policyInfo == nil {
		return errors.NewErr("此政策已过期, 无法作废")
	}
	if policyInfo.Archive == 1 {
		return errors.NewErr("此政策已归档, 无法作废")
	}
	order, err := a.repo.ReimbursementApplyOrder(c, req.ID)
	if err != nil {
		return err
	}
	if order == nil {
		return errors.NewErr("未找到此申请单，请确认")
	}
	if order.Status != 0 {
		return errors.NewErr("申请单状态不允许作废")
	}
	return a.repo.ApplyOrderSummaryInvalid(c, req.ID)
}

// ReimbursementSubmit 核销提交
func (a *reimbursement) ReimbursementSubmit(c *gin.Context, req *api.ApplyOrderSummarySubmitReq) error {
	policyInfo, err := a.repo.GetPolicyByOrder(c, req.IDs[0], 3) // 3表示核销小单类型
	if err != nil {
		return err
	}
	if policyInfo == nil {
		return errors.NewErr("此政策已过期, 无法作废")
	}
	if policyInfo.Archive == 1 {
		return errors.NewErr("此政策已归档, 无法作废")
	}
	// 检查政策时间范围
	now := time.Now()
	startTime := time.Time(policyInfo.StartTime)
	endTime := time.Time(policyInfo.EndTime)
	if now.Before(startTime) || now.After(endTime) {
		return errors.NewErr("已超出此政策核销时间")
	}
	standardType := policyInfo.StandardType

	// 查询支持标准
	balance, err := a.balanceRepo.GetBalanceStandard(c, req.PolicyID, req.CompanyID, standardType)
	if err != nil {
		return err
	}
	if balance == nil {
		return errors.NewErr("此公司未有支持标准")
	}

	// 检查标准额度
	switch standardType {
	case "standard_amount":
		if req.ReimbursementAmount > balance.Balance {
			return errors.NewErr("支持标准金额不足抵扣核销金额")
		}
	case "standard_quantity":
		if float64(req.ReimbursementQuantity) > balance.Balance {
			return errors.NewErr("支持标准数量不足抵扣核销金额")
		}
	default:
		if req.ReimbursementAmount > balance.Balance {
			return errors.NewErr("广告余额不足抵扣核销金额")
		}
	}
	//核销详情
	details, err := a.repo.GetOrdersDetail(c, req.CompanyID, req.IDs)
	topAgency := details[0].TopAgency
	companyID := details[0].CompanyID
	policyID := details[0].PolicyID
	req.UID = c.GetUint("uid")
	req.TopAgency = topAgency
	req.Code = details[0].Code
	req.Company = details[0].Company
	req.PolicyID = policyID

	var orders []*dao.OrderItem

	for _, detail := range details {
		if detail.TopAgency != topAgency {
			return errors.NewErr("同一个代理商才可创建核销单")
		}
		if detail.CompanyID != companyID {
			return errors.NewErr("同一个公司才可创建核销单")
		}
		if detail.Status != 0 {
			return errors.NewErr("小单状态不符，无法创建核销单")
		}
		if detail.TurnType != 1 && detail.PolicyID != policyID {
			return errors.NewErr("同一种政策才可创建核销单")
		}

		// 计算金额
		var amountValue float64
		if detail.ReimbursementApplyAmount != 0 && detail.ReimbursementApplyAmount > 0 {
			amountValue = detail.ReimbursementApplyAmount
		} else {
			amountValue = detail.Amount / 2
		}

		// 根据不同标准类型创建订单项
		orderItem := &dao.OrderItem{
			ID:       detail.ID,
			TurnType: detail.TurnType,
		}

		switch req.StandardType {
		case "standard_quantity":
			orderItem.Amount = amountValue
			orderItem.Quantity = detail.QuantityTotal
		case "standard_balance_quota":
			orderItem.Amount = detail.Amount
			orderItem.Quantity = 0
		default:
			orderItem.Amount = amountValue
			orderItem.Quantity = 0
		}

		orders = append(orders, orderItem)
	}
	//订单核销
	err = a.repo.ReimbursementCreate(c, req, policyInfo, orders, balance)
	if err == nil {
		//发送通知
	}
	return err
}
func (a *reimbursement) ReimbursementRetrial(c *gin.Context, req *api.ReimbursementRetrialReq) error {
	// 获取核销小单详情
	order, err := a.repo.GetOrderDetail(c, req.ReimbursementID)
	if err != nil {
		return err
	}
	if order == nil || order.ID == 0 {
		return errors.NewErr("未找到此申请单，请确认")
	}
	if order.Status != 1 {
		return errors.NewErr("大单不是核销确认状态")
	}

	policyInfo, err := a.repo.GetReimbursementPolicy(c, order.PolicyID)
	if err != nil {
		return err
	}
	if policyInfo.Archive == 1 {
		return errors.NewErr("此政策已归档, 无法确认核销金额")
	}
	// 查询支持标准
	balance, err := a.balanceRepo.GetBalanceStandard(c, order.PolicyID, order.CompanyID, policyInfo.StandardType)
	if err != nil {
		return err
	}
	if balance == nil {
		return errors.NewErr("此公司未有支持标准")
	}
	uid := c.GetUint("uid")
	// 检测小单 去掉关联  减少大单金额或者数量   小单回退
	return a.repo.ReimbursementRetrial(c, uid, req.ReimbursementID, req.SummaryIDs, order, policyInfo, balance)
}
func (a *reimbursement) ReimbursementAudit(c *gin.Context, req *api.ReimbursementAuditReq) error {
	// 获取核销小单详情
	order, err := a.repo.GetOrderDetail(c, req.ID)
	if err != nil {
		return err
	}
	if order == nil || order.ID == 0 {
		return errors.NewErr("未找到此申请单，请确认")
	}
	if order.Status != 1 {
		return errors.NewErr("大单不是核销确认状态")
	}

	policyInfo, err := a.repo.GetReimbursementPolicy(c, req.ID)
	if err != nil {
		return err
	}
	if policyInfo.Archive == 1 {
		return errors.NewErr("此政策已归档, 无法确认核销金额")
	}
	// 查询支持标准
	balance, err := a.balanceRepo.GetBalanceStandard(c, order.PolicyID, order.CompanyID, policyInfo.StandardType)
	if err != nil {
		return err
	}
	if balance == nil {
		return errors.NewErr("此公司未有支持标准")
	}
	uid := c.GetUint("uid")
	req.Uid = uid
	return a.repo.ReimbursementAudit(c, req, order, policyInfo, balance)

}

// reimbursementSMSPush 核销信息推送
func reimbursementSMSPush(uid int, msg, url string) error {
	h5URL := H5URL{
		Name:  "市场核销",
		URL:   url,
		Icon:  "https://dt.readboy.com/rbcare/h5/icon/20210302093509.png",
		Index: "https://h5-yx.readboy.com/manage/reimbursement",
		Key:   "verification",
	}

	// 将h5URL转换为JSON字符串
	h5URLJson, err := json.Marshal(h5URL)
	if err != nil {
		return fmt.Errorf("marshal h5URL error: %w", err)
	}

	// 构建推送消息体
	body := PushBody{
		Platform: []string{"ios", "android"},
		Audience: Audience{
			Users: []string{strconv.Itoa(uid)},
		},
		Title:   "【核销政策待处理】",
		Content: msg,
		Slug:    "verification",
		Action:  "forward",
		URL:     "h5://" + string(h5URLJson),
	}
	fmt.Println(body)

	// 调用消息推送服务
	return nil
}
