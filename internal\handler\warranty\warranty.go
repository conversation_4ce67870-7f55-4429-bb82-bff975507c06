package warranty

import (
	"github.com/gin-gonic/gin"
	api "marketing/internal/api/warranty"
	"marketing/internal/consts"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	warranty "marketing/internal/service"
	"strconv"
)

type WarrantyHandler interface {
	ChangeStatus(c *gin.Context)
	Import(c *gin.Context)
	ImportGroup(c *gin.Context)
	DoImport(c *gin.Context)
	DoImportGroup(c *gin.Context)
	CancelInsurance(c *gin.Context)
	Return(c *gin.Context)
	DoReturn(c *gin.Context)
	Exchange(c *gin.Context)
	Endpoints(c *gin.Context)
	WarrantyExport(c *gin.Context)

	Index(c *gin.Context)
	Create(c *gin.Context)
	List(c *gin.Context)
	UpdateAssessment(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
}

type warrantyHandler struct {
	warrantyService warranty.InterfaceWarranty
}

func NewWarrantyHandler(svc warranty.InterfaceWarranty) WarrantyHandler {
	return &warrantyHandler{
		warrantyService: svc,
	}
}

func (w *warrantyHandler) ChangeStatus(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w *warrantyHandler) Import(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w *warrantyHandler) ImportGroup(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w *warrantyHandler) DoImport(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w *warrantyHandler) DoImportGroup(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w *warrantyHandler) CancelInsurance(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w *warrantyHandler) Return(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w *warrantyHandler) DoReturn(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w *warrantyHandler) Exchange(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w *warrantyHandler) Endpoints(c *gin.Context) {
	q := c.Query("q")
	endpoints := w.warrantyService.GetEndpoints(c, q)
	if len(endpoints) == 0 {
		handler.Success(c, "无搜索结果")
		return
	}
	handler.Success(c, endpoints)
}

func (w *warrantyHandler) WarrantyExport(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w *warrantyHandler) Index(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w *warrantyHandler) Create(c *gin.Context) {
	var req *api.CreateWarrantyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	data, err := w.warrantyService.Create(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

func (w *warrantyHandler) List(c *gin.Context) {
	var warrantyInfo *api.WarrantyInfoReq
	if err := c.ShouldBind(&warrantyInfo); err != nil {
		handler.Error(c, err)
		return
	}
	resp, err := w.warrantyService.GetInfo(c, warrantyInfo)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, resp)
}

func (w *warrantyHandler) UpdateAssessment(c *gin.Context) {
	var id int
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	action, err := strconv.Atoi(c.Param("assessment"))
	if err != nil || (action != consts.WarrantyAssessment && action != consts.WarrantyNotAssessment) {
		handler.Error(c, errors.NewErr("参数错误"))
		return
	}
	err = w.warrantyService.UpdateAssessment(c, id, uint(action))
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (w *warrantyHandler) Update(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}
	var updateReq *api.EditReq
	if err = c.ShouldBind(&updateReq); err != nil {
		handler.Error(c, err)
	}
	rowsAffected, err := w.warrantyService.Update(c, id, updateReq)
	if err != nil || rowsAffected == 0 {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (w *warrantyHandler) Delete(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}
