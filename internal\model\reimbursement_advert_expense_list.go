package model

import (
	"time"
)

type ReimbursementAdvertExpenseList struct {
	ID                   int        `gorm:"column:id;primaryKey;autoIncrement"`
	SN                   string     `gorm:"column:sn"`                     // 核销单号
	UID                  int        `gorm:"column:uid"`                    // 用户id, 该id来自admin_users表
	TopAgency            int        `gorm:"column:top_agency"`             // 一级代理id
	SecondAgency         int        `gorm:"column:second_agency"`          // 二级代理id
	PolicyID             int        `gorm:"column:policy_id"`              // 政策id
	MaterielSource       int        `gorm:"column:materiel_source"`        // 1--当地制作，2--采购的物料
	ApplicationInfo      *string    `gorm:"column:application_info"`       // 申请资料
	Amount               float64    `gorm:"column:amount"`                 // 申请金额
	ActualAmount         float64    `gorm:"column:actual_amount"`          // 实际核销金额
	Status               int        `gorm:"column:status"`                 // 资料审核状态
	CreatedAt            time.Time  `gorm:"column:created_at"`             // 创建时间
	UpdatedAt            *time.Time `gorm:"column:updated_at"`             // 更新时间
	AuditTime            *time.Time `gorm:"column:audit_time"`             // 审批时间
	Remark               *string    `gorm:"column:remark"`                 // 审核不通过原因
	AuditMan             *int       `gorm:"column:audit_man"`              // 申请审批人
	Rollback             int        `gorm:"column:rollback"`               // 是否回退
	CompanyID            int        `gorm:"column:company_id"`             // 公司id
	Code                 string     `gorm:"column:code"`                   // 客户编码
	Company              string     `gorm:"column:company"`                // 公司名称
	ReimbursementType    int        `gorm:"column:reimbursement_type"`     // 核销类型
	MaterialReturnStatus int        `gorm:"column:material_return_status"` // 材料回寄状态
	CompletionStatus     int        `gorm:"column:completion_status"`      // 完成状态
	ExpressComeSn        *string    `gorm:"column:express_come_sn"`        // 物流单号
	ExpressComeCom       *string    `gorm:"column:express_come_com"`       // 物流公司
	ExpressComeTime      *time.Time `gorm:"column:express_come_time"`      // 物流发货时间
	CompletionTime       *time.Time `gorm:"column:completion_time"`        // 完成时间
}

// TableName sets the insert table name for this struct type
func (ReimbursementAdvertExpenseList) TableName() string {
	return "reimbursement_advert_expense_list"
}
